const fs = require('fs')
const path = require('path')

const [, , ...args] = process.argv
const buildOptions = {
  target: args[0] || 'html'
}

const inputs = ['landing', 'header', 'sidebar', 'vip-page', 'live-casino-page', 'rewards-page']

for (const input of inputs) {
  const html = fs.readFileSync(path.join(__dirname, 'public', input, 'index.html'), 'utf8')
  const styles = fs
    .readdirSync(path.join(__dirname, 'public', input))
    .filter(file => file.endsWith('.css') && file.startsWith('_') === false)
    .sort((a, b) => parseInt(a) - parseInt(b))
    .map(f => fs.readFileSync(path.join(__dirname, 'public', input, f), 'utf-8').trim())
    .filter(f => f.length !== 0)
  const scripts = fs
    .readdirSync(path.join(__dirname, 'public', input))
    .filter(file => file.endsWith('.js') && file.startsWith('_') === false)
    .sort((a, b) => parseInt(a) - parseInt(b))
    .map(f => fs.readFileSync(path.join(__dirname, 'public', input, f), 'utf-8').trim())
    .map(s =>
      s
        .split('\n')
        .map(l => (l.trim().startsWith('//') ? '' : l))
        .join('\n')
    )
    .map(s => `(() => {\n${s}\n})();`)
    .filter(f => f.length !== 0)

  switch (buildOptions.target) {
    case 'js-inline':
      {
        let bundle = ''
        bundle += `${styles.map(s => `<style>\n${s}\n</style>`).join('\n')}`
        bundle += `\n${html}\n`

        const scriptsData = JSON.stringify(scripts.join('\n')).slice(1, -1)
        const init = `{ let a = document.createElement('script'); a.textContent = '${scriptsData}'; document.head.append(a); }`
        bundle = bundle.replace('{{ $ON_ERROR }}', init)

        const jsBundle = `document.querySelector('.page-wrapper .main-wrapper').innerHTML = ${JSON.stringify(bundle)}`

        fs.writeFileSync(path.join(__dirname, 'dist', `${input}.js`), jsBundle)
      }
      break

    case 'html-inline':
      {
        let bundle = ''
        bundle += `${styles.map(s => `<style>\n${s}\n</style>`).join('\n')}`
        bundle += `\n${html}\n`

        const scriptsData = JSON.stringify(scripts.join('\n')).slice(1, -1)
        const init = `{ let a = document.createElement('script'); a.textContent = '${scriptsData}'; document.head.append(a); }`
        bundle = bundle.replace('{{ $ON_ERROR }}', init)

        fs.writeFileSync(path.join(__dirname, 'dist', `${input}.html`), bundle)
      }
      break

    case 'html':
      {
        let bundle = ''
        bundle += `${styles.map(s => `<style>\n${s}\n</style>`).join('\n')}`
        bundle += `\n${html}\n`.replace('{{ $ON_ERROR }}', '')
        bundle += `${scripts.map((s, idx) => `<script nonce="makrobet-${idx}">\n${s}\n</script>`).join('\n')}`

        fs.writeFileSync(path.join(__dirname, 'dist', `${input}.html`), bundle)
      }
      break

    default:
      throw new Error(`Invalid build target: ${buildOptions.target}`)
  }

  console.log(`Compiled "${buildOptions.target}" target!`)
}
