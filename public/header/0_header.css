/* Header Styles - Flat Modern Design */
.page-footer {}

.page-footer>* {
  max-width: calc(1400px - 40px);
  padding: 0 20px;
  margin: 0 auto;
}

#header .hdr-cntr-wrapper-top {
  background: #2b2b4f;
}

#header .hdr-cntr-wrapper-top .hdr-first-row {
  max-width: calc(1400px - 48px);
  margin: 0 auto;
  padding: 0;
  border-bottom: 1px solid #3F3F5F;
}

#header .hdr-cntr-wrapper-bottom {
  background: #404060;
}

#header .hdr-cntr-wrapper-bottom .hdr-bottom {
  max-width: 1400px;
  margin: 0 auto;
}

.header-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
  position: relative;
}

.makrobet-header {
  position: relative;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgb(var(--secondary-color));
  border-bottom: 1px solid rgba(var(--primary-color), 0.15);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.makrobet-header.fixed {
  position: fixed;
  top: 0;
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
  }

  to {
    transform: translateY(0);
  }
}

.makrobet-header:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.header-container {
  width: 100%;
  padding: 0 24px;
  display: flex;
  align-items: center;
  height: 64px;
}

/* Logo Styles */
.header-logo {
  flex-shrink: 0;
  z-index: 1001;
}

.logo-link {
  display: block;
  text-decoration: none;
  transition: opacity 0.2s ease;
}

.logo-link:hover {
  opacity: 0.8;
}

.logo-image {
  height: 40px;
  width: auto;
  object-fit: contain;
  display: block;
}

/* Navigation Styles */
.header-nav {
  display: flex;
  align-items: center;
  margin-left: 1rem;
  flex: 1;
  justify-content: space-between;
  gap: 40px;
}

.nav-menu {
  display: flex;
  align-items: center;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 4px;
}

/* Search Styles */
.header-search-container {
  position: relative;
  width: 320px;
  max-width: 320px;
}

.search-input-wrapper {
  position: relative;
  width: 100%;
}

.game-search-input {
  width: 100% !important;
  height: 48px !important;
  margin: 0 !important;
  padding: 14px 20px !important;
  background: rgba(var(--secondary-color), 0.8) !important;
  border: 2px solid rgba(var(--primary-color), 0.3) !important;
  border-radius: 24px !important;
  color: rgba(255, 255, 255, 0.95) !important;
  font-size: 0.9rem !important;
  font-weight: 500 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  outline: none !important;
  backdrop-filter: blur(10px) !important;
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

.game-search-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
  font-weight: 400;
}

.game-search-input:focus {
  background: rgba(var(--secondary-color), 0.9);
  border-color: rgb(var(--primary-color));
  box-shadow:
    0 0 0 3px rgba(var(--primary-color), 0.15),
    0 8px 24px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.game-search-input:hover:not(:focus) {
  background: rgba(var(--secondary-color), 0.85);
  border-color: rgba(var(--primary-color), 0.5);
  transform: translateY(-0.5px);
}

.search-results {
  position: absolute;
  top: calc(100% + 12px);
  left: 0;
  right: 0;
  background: linear-gradient(135deg, rgba(20, 20, 30, 0.98) 0%, rgba(15, 15, 25, 0.95) 100%);
  border: 1px solid rgba(var(--primary-color), 0.25);
  border-radius: 16px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.4),
    0 8px 24px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  max-height: 400px;
  overflow-y: auto;
  overflow-x: hidden;
  z-index: 1003;
  display: none;
  animation: searchResultsSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes searchResultsSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.search-result-item {
  display: flex;
  align-items: center;
  padding: 14px 18px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.06);
  position: relative;
  overflow: hidden;
}

.search-result-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 0;
  background: linear-gradient(90deg, rgba(var(--primary-color), 0.2) 0%, transparent 100%);
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.search-result-item:last-child {
  border-bottom: none;
}

.search-result-item:hover {
  background: rgba(var(--primary-color), 0.08);
  transform: translateX(4px);
}

.search-result-item:hover::before {
  width: 4px;
}

.search-result-item:active {
  transform: translateX(2px) scale(0.98);
}

.search-result-image {
  width: 52px;
  height: 36px;
  object-fit: cover;
  border-radius: 8px;
  margin-right: 16px;
  flex-shrink: 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.search-result-item:hover .search-result-image {
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
}

.search-result-content {
  flex: 1;
  min-width: 0;
}

.search-result-name {
  font-size: 0.9rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.search-result-type {
  font-size: 0.75rem;
  color: rgba(var(--primary-color), 0.8);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.search-no-results {
  padding: 20px 16px;
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.9rem;
}



/* Expanded hover area to bridge nav item and dropdown */
.nav-item::before {
  content: '';
  position: absolute;
  top: calc(100% - 20px);
  height: 120px;
  left: -10px;
  right: -10px;
  /* bottom: -20px; */
  z-index: 999;
  pointer-events: none;
}

.nav-item:hover::before {
  pointer-events: auto;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 14px 20px;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-radius: 6px;
  transition: color 0.2s ease;
  white-space: nowrap;
  position: relative;
  background: transparent;
}

.nav-item:hover .nav-link {
  color: rgb(var(--primary-color));
}

.nav-link.has-children::after {
  content: '▼';
  margin-left: 8px;
  font-size: 0.65rem;
  transition: transform 0.2s ease;
  opacity: 0.7;
}

.nav-item:hover .nav-link.has-children::after {
  transform: rotate(180deg);
}

.nav-link.new::after {
  content: 'YENİ';
  position: absolute;
  top: -2px;
  right: -2px;
  background: rgb(var(--primary-color));
  color: rgb(var(--secondary-color));
  font-size: 0.6rem;
  padding: 2px 4px;
  border-radius: 3px;
  font-weight: 600;
}

/* Mega Menu Styles */
.dropdown-menu {
  position: absolute;
  top: calc(100% - 1px);
  /* Slight overlap to eliminate gap */
  left: 20px;
  right: 20px;
  background: rgba(20, 20, 30, 0.95);
  border-top: 1px solid rgba(var(--primary-color), 0.2);
  border-bottom: 1px solid rgba(var(--primary-color), 0.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.2s ease;
  z-index: 1002;
  list-style: none;
  margin: 0;
  padding: 24px 0;
  border-radius: 0 0 12px 12px;
  backdrop-filter: blur(10px);
}

.mega-menu {
  padding: 20px 0;
}

.nav-item:hover .dropdown-menu,
.dropdown-menu:hover {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-menu .dropdown-container {
  width: 100%;
  margin: 0;
  padding: 0 24px;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 16px;
  align-items: flex-start;
  justify-content: flex-start;
}

.mega-menu-container {
  width: 100%;
  margin: 0;
  padding: 0 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Mega Menu Groups Grid - 2 Columns with auto-spanning */
.mega-menu-groups-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 24px;
  width: 100%;
}

/* Single group spans both columns */
.mega-menu-groups-grid .mega-menu-group:only-child {
  grid-column: 1 / -1;
}

/* Mega Menu Group Styles - Simple without background */
.mega-menu-group {
  width: 100%;
}

.mega-menu-group-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 0 12px 0;
  margin-bottom: 16px;
  border-bottom: 2px solid rgba(var(--primary-color), 0.3);
}

.mega-menu-group-title {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 700;
  color: rgb(var(--primary-color));
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Navigation Controls */
.mega-menu-nav-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.mega-menu-nav-btn {
  background: rgba(var(--primary-color), 0.1);
  border: 1px solid rgba(var(--primary-color), 0.2);
  color: rgb(var(--primary-color));
  width: 28px;
  height: 28px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.2s ease;
}

.mega-menu-nav-btn:hover:not(:disabled) {
  background: rgba(var(--primary-color), 0.2);
  border-color: rgba(var(--primary-color), 0.3);
}

.mega-menu-nav-btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

.mega-menu-see-all {
  background: rgba(var(--primary-color), 0.1);
  border: 1px solid rgba(var(--primary-color), 0.2);
  color: rgb(var(--primary-color));
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.mega-menu-see-all:hover {
  background: rgba(var(--primary-color), 0.2);
  border-color: rgba(var(--primary-color), 0.3);
}

.mega-menu-group-content {
  padding: 0;
}

/* 2x2 Grid Layout (4 columns when single group) */
.mega-menu-items-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 16px;
}

/* 4 columns when there's only one group in the row */
.mega-menu-groups-grid .mega-menu-group:only-child .mega-menu-items-grid {
  grid-template-columns: repeat(4, 1fr);
}

.mega-menu-item {
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.3s ease;
}

.nav-item:hover .mega-menu-item,
.dropdown-menu:hover .mega-menu-item {
  opacity: 1;
  transform: translateY(0);
}

.nav-item:hover .mega-menu-item:nth-child(1),
.dropdown-menu:hover .mega-menu-item:nth-child(1) {
  transition-delay: 0.05s;
}

.nav-item:hover .mega-menu-item:nth-child(2),
.dropdown-menu:hover .mega-menu-item:nth-child(2) {
  transition-delay: 0.1s;
}

.nav-item:hover .mega-menu-item:nth-child(3),
.dropdown-menu:hover .mega-menu-item:nth-child(3) {
  transition-delay: 0.15s;
}

.nav-item:hover .mega-menu-item:nth-child(4),
.dropdown-menu:hover .mega-menu-item:nth-child(4) {
  transition-delay: 0.2s;
}

.mega-menu-link {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  font-size: 0.85rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-radius: 8px;
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.07);
  border: 1px solid rgba(255, 255, 255, 0.08);
  text-align: left;
  position: relative;
  gap: 12px;
}

.mega-menu-icon {
  width: 40px;
  height: 40px;
  object-fit: contain;
  flex-shrink: 0;
  opacity: 0.8;
  transition: all 0.2s ease;
  border-radius: 4px;
}

.mega-menu-link:hover .mega-menu-icon {
  opacity: 1;
  transform: scale(1.05);
}

.mega-menu-text {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.mega-menu-link:hover {
  background: rgba(var(--primary-color), 0.1);
  color: rgb(var(--primary-color));
  border-color: rgba(var(--primary-color), 0.2);
}



/* Legacy dropdown styles for backward compatibility */
.dropdown-item {
  margin: 0;
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.2s ease;
}

.nav-item:hover .dropdown-item,
.dropdown-menu:hover .dropdown-item {
  opacity: 1;
  transform: translateY(0);
}

.dropdown-link {
  display: flex;
  align-items: center;
  padding: 16px 24px;
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-radius: 8px;
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.07);
  border: 1px solid rgba(255, 255, 255, 0.08);
  text-align: left;
  min-width: 200px;
  position: relative;
  gap: 12px;
}

.dropdown-icon {
  width: 40px;
  height: 40px;
  object-fit: contain;
  flex-shrink: 0;
  opacity: 0.8;
  transition: all 0.2s ease;
  border-radius: 4px;
}

.dropdown-link:hover .dropdown-icon {
  opacity: 1;
  transform: scale(1.05);
}

.dropdown-text {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dropdown-link:hover {
  background: rgba(var(--primary-color), 0.1);
  color: rgb(var(--primary-color));
  border-color: rgba(var(--primary-color), 0.2);
  transform: translateY(-2px);
}

.dropdown-link::after {
  content: '→';
  font-size: 0.75rem;
  opacity: 0;
  transition: all 0.2s ease;
  color: rgb(var(--primary-color));
  flex-shrink: 0;
  margin-left: auto;
}

.dropdown-link:hover::after {
  opacity: 1;
}



/* Overflow Dropdown */
.overflow-dropdown {
  position: relative;
}

.overflow-toggle {
  background: rgba(var(--primary-color), 0.08);
  border: 1px solid rgba(var(--primary-color), 0.2);
  color: rgb(var(--primary-color));
  padding: 14px 18px;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.overflow-toggle:hover {
  background: rgba(var(--primary-color), 0.15);
  border-color: rgba(var(--primary-color), 0.3);
}



.overflow-dropdown:hover .dropdown-menu,
.overflow-dropdown .dropdown-menu:hover {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

/* Nested dropdowns in overflow menu */
.overflow-dropdown .dropdown-item {
  position: relative;
}

.overflow-dropdown .dropdown-item .dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  width: 100vw;
  margin: 0;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-8px);
  transition: all 0.25s ease;
}

.makrobet-header.fixed .overflow-dropdown .dropdown-item .dropdown-menu {
  position: fixed;
  top: 64px;
  max-width: 1400px;
  margin: 0 auto;
  left: 50%;
  transform: translateX(-50%);
  right: auto;
}

/* Only show nested menu when hovering the specific dropdown item */
.overflow-dropdown .dropdown-item:hover>.dropdown-menu,
.overflow-dropdown .dropdown-item .dropdown-menu:hover {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.overflow-dropdown .dropdown-link.has-children::after {
  content: '▶';
  margin-left: auto;
  font-size: 0.65rem;
  transition: transform 0.2s ease;
  opacity: 0.7;
}

.overflow-dropdown .dropdown-item:hover>.dropdown-link.has-children::after {
  transform: rotate(90deg);
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  justify-content: space-around;
  width: 28px;
  height: 28px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  z-index: 1001;
}

.hamburger-line {
  width: 100%;
  height: 2px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 1px;
  transition: all 0.25s ease;
}

.mobile-menu-toggle:hover .hamburger-line {
  background: rgb(var(--primary-color));
}

.mobile-menu-toggle.active .hamburger-line:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-toggle.active .hamburger-line:nth-child(2) {
  opacity: 0;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .header-container {
    padding: 0 20px;
  }

  .nav-link {
    padding: 12px 16px;
    font-size: 0.8rem;
    font-weight: 700;
  }

  .dropdown-menu .dropdown-container {
    padding: 0 20px;
    gap: 8px;
  }

  /* Search responsive */
  .header-search-container {
    width: 280px;
    max-width: 280px;
  }

  .game-search-input {
    height: 44px;
    font-size: 0.85rem;
    padding: 12px 18px;
  }
}

@media (max-width: 1024px) {
  .nav-link {
    padding: 12px 14px;
    font-size: 0.75rem;
    font-weight: 700;
  }

  .dropdown-link {
    max-width: none;
    min-width: auto;
    width: 100%;
  }

  /* Search responsive */
  .header-search-container {
    width: 240px;
    max-width: 240px;
  }

  .game-search-input {
    height: 42px;
    font-size: 0.8rem;
    padding: 11px 16px;
  }

  /* Tablet Mega Menu Adjustments */
  .mega-menu-groups-grid {
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 24px;
  }

  /* Single group spans both columns on tablet */
  .mega-menu-groups-grid .mega-menu-group:only-child {
    grid-column: 1 / -1;
  }

  .mega-menu-group-title {
    font-size: 1rem;
  }

  /* Tablet Navigation Controls */
  .mega-menu-nav-btn {
    width: 24px;
    height: 24px;
    font-size: 14px;
  }

  .mega-menu-see-all {
    padding: 4px 8px;
    font-size: 0.7rem;
  }

  .mega-menu-items-grid {
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 12px;
  }

  /* 4 columns when there's only one group in the row on tablet */
  .mega-menu-groups-grid .mega-menu-group:only-child .mega-menu-items-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .mega-menu-link {
    padding: 14px 16px;
    font-size: 0.8rem;
    font-weight: 700;
    background: rgba(255, 255, 255, 0.07);
    border: 1px solid rgba(255, 255, 255, 0.08);
  }

  .mega-menu-icon {
    width: 36px;
    height: 36px;
  }
}

@media (max-width: 768px) {
  .header-container {
    padding: 0 16px;
    height: 56px;
  }

  .logo-image {
    height: 32px;
  }

  .mobile-menu-toggle {
    display: flex;
  }

  /* Hide search on mobile */
  .header-search-container {
    display: none;
  }

  .nav-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: rgb(var(--secondary-color));
    flex-direction: column;
    align-items: stretch;
    padding: 20px 16px;
    gap: 0;
    transform: translateY(-100%);
    transition: transform 0.3s ease;
    border-bottom: 1px solid rgba(var(--primary-color), 0.15);
    max-height: calc(100vh - 56px);
    overflow-y: auto;
    visibility: hidden;
    opacity: 0;
  }

  .nav-menu.active {
    transform: translateY(0);
    visibility: visible;
    opacity: 1;
  }

  .makrobet-header.fixed .nav-menu {
    position: fixed;
    top: 56px;
  }



  .nav-item {
    width: 100%;
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  }

  .nav-item:last-child {
    border-bottom: none;
  }

  .nav-link {
    width: 100%;
    padding: 18px 0;
    justify-content: space-between;
    border-radius: 0;
    font-size: 0.9rem;
    font-weight: 700;
  }

  .dropdown-menu {
    position: static;
    opacity: 1;
    visibility: visible;
    transform: none;
    box-shadow: none;
    border: none;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 0;
    margin-top: 0;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    padding: 0;
  }

  .dropdown-menu .dropdown-container {
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 0;
  }

  .nav-item.active .dropdown-menu {
    max-height: 600px;
    padding: 12px 0;
  }

  /* Mobile Mega Menu Styles */
  .mega-menu-container {
    padding: 0;
    gap: 12px;
  }

  /* Mobile Groups - Stack vertically */
  .mega-menu-groups-grid {
    grid-template-columns: 1fr;
    grid-gap: 20px;
  }

  .mega-menu-group-header {
    padding: 0 0 8px 0;
    margin-bottom: 12px;
  }

  .mega-menu-group-title {
    font-size: 0.95rem;
  }

  /* Mobile Navigation Controls */
  .mega-menu-nav-controls {
    gap: 6px;
  }

  .mega-menu-nav-btn {
    width: 24px;
    height: 24px;
    font-size: 14px;
  }

  .mega-menu-see-all {
    padding: 4px 8px;
    font-size: 0.65rem;
  }

  .mega-menu-group-content {
    padding: 0;
  }

  /* Mobile 2x2 Grid (keep 2 columns even for single group to avoid cramped layout) */
  .mega-menu-items-grid {
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 8px;
  }

  .mega-menu-link {
    padding: 16px 12px;
    font-size: 0.75rem;
    font-weight: 700;
    gap: 8px;
    text-align: center;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.05);
  }

  .mega-menu-icon {
    width: 32px;
    height: 32px;
  }

  .mega-menu-text {
    white-space: normal;
    text-align: center;
    line-height: 1.2;
  }



  /* Legacy dropdown styles for mobile */
  .dropdown-link {
    padding: 16px 20px;
    font-size: 0.85rem;
    max-width: none;
    min-width: auto;
    width: 100%;
    transform: none;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.03);
    gap: 10px;
  }

  .dropdown-link:hover {
    transform: none;
    background: rgba(var(--primary-color), 0.1);
  }

  .dropdown-link::after {
    display: none;
  }

  .dropdown-icon {
    width: 28px;
    height: 28px;
  }

  .dropdown-link:hover .dropdown-icon {
    transform: scale(1.05);
  }

  /* Overflow dropdown mobile styles */
  .overflow-dropdown .dropdown-menu {
    position: static;
    margin-left: 0;
  }

  .overflow-dropdown .dropdown-item .dropdown-menu {
    position: static;
    margin-left: 20px;
    border-left: 2px solid rgba(var(--primary-color), 0.2);
    padding-left: 12px;
  }

  .overflow-toggle {
    width: 100%;
    text-align: left;
    background: rgba(var(--primary-color), 0.08);
    border: 1px solid rgba(var(--primary-color), 0.2);
    padding: 18px 0;
    border-radius: 0;
    font-size: 0.9rem;
  }

  body.header-fixed {
    padding-top: 56px;
  }
}

/* Remove default body padding since header is not fixed by default */
body {
  padding-top: 0;
}

body.header-fixed {
  padding-top: 64px;
}

/* Tournament Menu Item Styles */
.mega-menu-link.tournament-item {
  padding: 0;
  border-radius: 8px;
  overflow: hidden;
  display: block;
  height: 100%;
  background: transparent;
  border: none;
}

.tournament-card {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  transition: transform 0.2s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.tournament-card:hover {
  transform: scale(1.02);
}

.tournament-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: top;
  transition: opacity 0.2s ease;
}

.tournament-name {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: #FFD700;
  padding: 12px 8px 8px;
  font-size: 0.85rem;
  font-weight: 600;
  text-align: center;
  line-height: 1.2;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

/* Adjust mega menu item sizing for tournaments */
.mega-menu-group:has(.tournament-item) .mega-menu-item {
  height: 170px;
}

.mega-menu-group:has(.tournament-item) .mega-menu-items-grid {
  gap: 12px;
}

/* Mobile tournament styles */
@media (max-width: 768px) {
  .mega-menu-link.tournament-item {
    height: 100px;
  }

  .mega-menu-group:has(.tournament-item) .mega-menu-item {
    height: 100px;
  }

  .tournament-name {
    font-size: 0.75rem;
    padding: 8px 6px 6px;
  }
}

/* Tombala Menu Item Styles */
.mega-menu-link.tombala-item {
  padding: 0;
  border-radius: 8px;
  overflow: hidden;
  display: block;
  height: 100%;
  background: transparent;
  border: none;
}

.tombala-card {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  transition: transform 0.2s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
}

.tombala-card:hover {
  transform: scale(1.02);
}

.tombala-image {
  width: 100%;
  height: 85%;
  transform: scale(1.01);
  object-fit: cover;
  object-position: center left;
  transition: opacity 0.2s ease;
}

.tombala-name {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.8);
  color: #FFD700;
  padding: 8px;
  font-size: 1rem;
  font-weight: 700;
  text-align: center;
  line-height: 1.2;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
  height: 15%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Adjust mega menu item sizing for tombala */
.mega-menu-group:has(.tombala-item) .mega-menu-item {
  height: 250px;
}

.mega-menu-group:has(.tombala-item) .mega-menu-items-grid {
  gap: 12px;
}

@media (max-width: 1280px) {
  .mega-menu-group:has(.tombala-item) .mega-menu-item {
    height: 195px;
  }
}

@media (max-width: 1024px) {
  .mega-menu-group:has(.tombala-item) .mega-menu-item {
    height: 185px;
  }
}

/* Mobile tombala styles */
@media (max-width: 768px) {
  .mega-menu-link.tombala-item {
    height: 120px;
  }

  .mega-menu-group:has(.tombala-item) .mega-menu-item {
    height: 120px;
  }

  .tombala-name {
    font-size: 0.75rem;
    padding: 6px;
  }
}
