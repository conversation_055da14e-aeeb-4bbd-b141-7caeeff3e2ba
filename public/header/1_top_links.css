/* Support Container Styles */
.support-container {
  position: relative;
  display: flex;
  align-items: center;
  gap: 6px;
  background: transparent;
  border: none;
  border-radius: 0;
  padding: 0;
  margin-left: 5px;
}

.support-content {
  display: flex;
  align-items: center;
  gap: 6px;
}

/* Support Links Column */
.support-links-column {
  display: flex;
  flex-direction: column;
  gap: 0px;
}

.support-link-item {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 2px 4px !important;
  background: transparent;
  border: none;
  border-radius: 0;
  color: rgb(var(--primary-color));
  text-decoration: none;
  transition: all 0.2s ease;
  font-size: 0.65rem;
  white-space: nowrap;
}

.support-link-item:hover {
  background: transparent;
  border: none;
  color: rgb(var(--primary-color));
  opacity: 0.8;
  transform: translateX(1px);
}

.support-link-item i {
  font-size: 0.7rem;
  width: 12px;
  text-align: center;
  flex-shrink: 0;
}

.support-link-text {
  font-weight: 400;
  letter-spacing: 0.1px;
  font-size: 0.6rem;
}

/* Call Me Dropdown Styles */
.call-me-dropdown {
  position: relative;
  display: inline-block;
}

.call-me-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: linear-gradient(135deg, #fbd12d 0%, #f4c430 100%);
  color: #2b2b4f;
  border: none;
  border-radius: 25px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(251, 209, 45, 0.3);
  user-select: none;
  white-space: nowrap;
}

.call-me-header:hover {
  background: linear-gradient(135deg, #f4c430 0%, #e6b800 100%);
  box-shadow: 0 4px 12px rgba(251, 209, 45, 0.4);
  transform: translateY(-1px);
}

.call-me-header:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(251, 209, 45, 0.3);
}

.call-me-header:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(251, 209, 45, 0.3);
}

.call-me-arrow {
  transition: transform 0.3s ease;
  font-size: 12px;
}

.call-me-arrow.rotated {
  transform: rotate(180deg);
}

.call-me-content {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  z-index: 9999;
  min-width: 200px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  margin-top: 8px;
}

.call-me-content.open {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.call-me-title {
  padding: 12px 16px 8px;
  font-size: 13px;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #f0f0f0;
  background: #f8f9fa;
  border-radius: 12px 12px 0 0;
}

.time-slots {
  padding: 8px 0;
  max-height: 240px;
  overflow-y: auto;
}

.time-slot {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  color: #333;
  border-left: 3px solid transparent;
}

.time-slot:hover {
  background: #fffbf0;
  border-left-color: #fbd12d;
  color: #2b2b4f;
}

.time-slot:active {
  background: #fff8e1;
}

.time-slot:focus {
  outline: none;
  background: #fffbf0;
  border-left-color: #fbd12d;
  color: #2b2b4f;
}

/* Phone icon styling */
.call-me-header .el-icon-phone-outline {
  font-size: 16px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .call-me-dropdown {
    margin-left: 5px;
  }

  .call-me-header {
    padding: 6px 12px;
    font-size: 13px;
  }

  .call-me-content {
    right: -20px;
    min-width: 180px;
  }

  .time-slot {
    padding: 8px 12px;
    font-size: 13px;
  }
}

/* Animation for smooth dropdown */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.call-me-content.open {
  animation: slideDown 0.3s ease;
}

/* Accessibility improvements */
.call-me-header[aria-expanded="true"] .call-me-arrow {
  transform: rotate(180deg);
}

.time-slot[tabindex="0"]:focus {
  outline: 2px solid #fbd12d;
  outline-offset: -2px;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .call-me-content {
    background: #2d3748;
    border-color: #4a5568;
    color: white;
  }

  .call-me-title {
    background: #1a202c;
    color: #e2e8f0;
    border-bottom-color: #4a5568;
  }

  .time-slot {
    color: #e2e8f0;
  }

  .time-slot:hover {
    background: #4a5568;
    color: #fbd12d;
  }
}

/* Loading state for time slots */
.time-slot.loading {
  opacity: 0.6;
  pointer-events: none;
}

.time-slot.selected {
  background: #fffbf0;
  color: #2b2b4f;
  border-left-color: #fbd12d;
  font-weight: 600;
}

.social-links {
  display: flex;
  align-items: center;
}

.social-links i {
  margin-right: 0 !important;
}

/* .social-links:not(.support) i {
  font-size: 20px !important;
} */

/* Social Container Styles */
.social-container {
  position: relative;
  display: flex;
  align-items: center;
  gap: 6px;
  /* background: rgba(59, 130, 246, 0.08); */
  /* border: 1px solid rgba(59, 130, 246, 0.2); */
  /* border-radius: 12px; */
  padding: 4px 4px;
  margin-right: 5px;
}

.social-content {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* Support Social Links Styles */
.support-container .social-links {
  gap: 4px;
}

.support-container .social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  /* background: rgba(var(--primary-color), 0.05); */
  /* border: 1px solid rgba(var(--primary-color), 0.15); */
  border-radius: 50%;
  color: rgb(var(--primary-color));
  text-decoration: none;
  transition: all 0.2s ease;
  font-size: 0.8rem;
}

.support-container .social-link:hover {
  background: rgba(var(--primary-color), 0.15);
  border-color: rgba(var(--primary-color), 0.3);
  color: rgb(var(--primary-color));
  transform: translateY(-1px);
}

/* Responsive support container */
@media (max-width: 768px) {
  .support-container {
    gap: 4px;
    padding: 0;
    margin-right: 4px;
  }

  .support-content {
    gap: 6px;
  }

  .support-links-column {
    gap: 2px;
  }

  .support-link-item {
    padding: 1px 2px;
    font-size: 0.6rem;
  }

  .support-link-item i {
    font-size: 0.65rem;
    width: 10px;
  }

  .support-link-text {
    font-size: 0.55rem;
  }

  .support-container .social-link {
    width: 24px;
    height: 24px;
    font-size: 0.75rem;
  }

  .social-container {
    gap: 8px;
    padding: 6px 12px;
    margin-right: 8px;
  }

  .social-content {
    gap: 8px;
  }

  .social-container .social-link {
    width: 24px;
    height: 24px;
    font-size: 0.75rem;
  }
}

/* Social Container Social Links Styles */
.social-container .social-links {
  gap: 4px;
}

.social-container .social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  /* background: rgba(59, 130, 246, 0.05); */
  /* border: 1px solid rgba(59, 130, 246, 0.15); */
  /* border-radius: 6px; */
  /* color: rgb(59, 130, 246); */
  text-decoration: none;
  transition: all 0.2s ease;
  /* font-size: 1.4rem; */
}

.social-container .social-link i {
  font-size: 1.4rem !important;
}
