window.$HEADER.onLifecycle({
  page: /^\/\w+\/games\/casino/,
  watch: '.gm-wrap',
  selector: `body`,
  onMount: (_, watchContainer, kill) => {
    window.addEventListener('@makroz/casino/provider_select', async e => {
      let buttons = watchContainer.querySelectorAll('.all-vendors a')

      if (buttons.length === 0) {
        watchContainer.querySelector('.vendor-t-c')?.click()
        await new Promise(resolve => setTimeout(resolve, 100))
      }

      buttons = watchContainer.querySelectorAll('.all-vendors a')
      if (buttons.length === 0) {
        return
      }

      for (const button of buttons) {
        if (button.textContent.trim().toLowerCase() === e.detail) {
          button.click()
          break
        }
      }
    })

    const providerName = new URLSearchParams(window.location.search).get('provider').trim()
    if (!providerName) {
      return
    }

    watchContainer.querySelector('.vendor-t-c')?.click()
    setTimeout(() => {
      const buttons = Array.from(watchContainer.querySelectorAll('.all-vendors a'))
      for (const button of buttons) {
        if (button.textContent.trim().toLowerCase() === providerName) {
          button.click()
          break
        }
      }
    }, 100)
  }
})

window.$HEADER.onLifecycle({
  page: /^\/\w+\/games\/casino/,
  watch: '.all-vendors',
  selector: `body`,
  onMount: (_, watchContainer, kill) => {
    const buttons = Array.from(watchContainer.querySelectorAll('.all-vendors a'))
    for (const button of buttons) {
      button.addEventListener('click', () => {
        const url = new URL(window.location.href)
        url.searchParams.set('provider', button.textContent.trim().toLowerCase())
        window.$HEADER.navigate(url)
      })
    }
  }
})
