window.$HEADER.onLifecycle({
  watch: `#PanelFront .bm_botbar`,
  selector: 'body',
  onMount: (_, watchContainer, kill) => {
    // watchContainer.style.display = 'none'
    const leftButtons = watchContainer.querySelector('.buttons_side')
    if (leftButtons) {
      leftButtons.style.display = 'none'
    }
    const bonusDemand = watchContainer.querySelector('.bonus_demand')
    if (bonusDemand) {
      bonusDemand.parentElement.style.display = 'none'
    }
  }
})
