// Marko Shop Makroz Hook - Intercepts the confirm order button and redirects requests
window.$HEADER.onLifecycle({
  watch: '.el-dialog__body .buy_now_cover .confirm_order_btn',
  selector: 'body',
  onMount: (_, watchContainer, kill) => {
    console.log('Confirm order button detected, setting up interceptor...')
    
    // Function to send POST request with market request body
    async function sendInterceptedRequest() {
      try {
        console.log('Sending intercepted POST request...')

        // Get required data from localStorage
        const pft = localStorage.getItem('cGFuZWw')
        const rawToken = localStorage.getItem('s7oryO9STV')
        const token = rawToken ? rawToken.replace(/"/g, '') : null
        const customerCode = localStorage.getItem('customerCode')

        console.log('Raw token from localStorage:', rawToken)
        console.log('Cleaned token:', token)

        if (!pft) {
          console.error('PFT not found in localStorage (key: cGFuZWw)')
          showNotification('PFT bulunamadı. Lütfen giriş yapın.', 'error')
          return false
        }

        if (!token) {
          console.error('Token not found in localStorage (key: s7oryO9STV)')
          showNotification('Token bulunamadı. Lütfen giriş yapın.', 'error')
          return false
        }

        if (!customerCode) {
          console.error('Customer code not found in localStorage (key: customerCode)')
          showNotification('Müşteri kodu bulunamadı. Lütfen giriş yapın.', 'error')
          return false
        }

        // Build request body
        const requestBody = {
          id: 16, // TODO: This will be provided later
          quantity: 1,
          token: token,
          pft: pft,
          cid: customerCode
        }

        console.log('Sending request body:', requestBody)

        const response = await fetch(window.$ENVIRONMENT.API_ENDPOINTS.MARKET_REQUESTS, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(requestBody)
        })

        if (response.ok) {
          const result = await response.json()
          console.log('Intercepted request sent successfully:', result)
          showNotification('İstek başarıyla gönderildi!', 'success')
          return true
        } else {
          console.error('Failed to send intercepted request:', response.status)
          showNotification('İstek gönderilirken bir hata oluştu.', 'error')
          return false
        }
      } catch (error) {
        console.error('Error sending intercepted request:', error)
        showNotification('İstek gönderilirken bir hata oluştu.', 'error')
        return false
      }
    }

    // Function to show notifications
    function showNotification(message, type = 'info') {
      // Remove existing notifications
      const existingNotifications = document.querySelectorAll('.confirm-order-notification')
      existingNotifications.forEach(notification => notification.remove())

      // Create notification element
      const notification = document.createElement('div')
      notification.className = `confirm-order-notification confirm-order-notification-${type}`
      notification.textContent = message
      
      // Style the notification
      Object.assign(notification.style, {
        position: 'fixed',
        top: '20px',
        right: '20px',
        padding: '12px 20px',
        borderRadius: '4px',
        color: 'white',
        fontWeight: 'bold',
        zIndex: '10000',
        maxWidth: '300px',
        wordWrap: 'break-word',
        backgroundColor: type === 'success' ? '#4CAF50' : '#f44336',
        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
      })

      document.body.appendChild(notification)

      // Auto remove after 5 seconds
      setTimeout(() => {
        if (notification.parentNode) {
          notification.remove()
        }
      }, 5000)
    }

    // Function to find and intercept the confirm order button
    function setupConfirmOrderInterception() {
      // Look for the specific button using the provided selector
      const confirmButton = document.querySelector('.el-dialog__body .buy_now_cover .confirm_order_btn')
      
      if (!confirmButton) {
        console.log('Confirm order button not found yet')
        return
      }

      console.log('Found confirm order button, setting up interception')
      
      // Check if we've already processed this button
      if (confirmButton.hasAttribute('data-confirm-intercepted')) {
        return
      }
      
      // Mark button as processed
      confirmButton.setAttribute('data-confirm-intercepted', 'true')
      
      // Store original onclick handler if it exists
      const originalOnClick = confirmButton.onclick
      
      // Store original event listeners (we can't access them directly, but we can prevent them)
      const originalEvents = []
      
      // Remove original onclick handler
      confirmButton.onclick = null
      
      // Add our custom click handler
      const handleConfirmClick = async (e) => {
        e.preventDefault()
        e.stopPropagation()
        e.stopImmediatePropagation()
        
        console.log('Confirm order button clicked, intercepting...')
        
        // Disable button temporarily to prevent double clicks
        const originalText = confirmButton.textContent
        const originalDisabled = confirmButton.disabled
        confirmButton.disabled = true
        confirmButton.textContent = 'Gönderiliyor...'
        
        try {
          const success = await sendInterceptedRequest()
          
          if (success) {
            // Keep button disabled for a few seconds after success
            setTimeout(() => {
              confirmButton.disabled = originalDisabled
              confirmButton.textContent = originalText
            }, 3000)
          } else {
            // Re-enable button immediately on failure
            confirmButton.disabled = originalDisabled
            confirmButton.textContent = originalText
          }
        } catch (error) {
          console.error('Error in confirm order button handler:', error)
          confirmButton.disabled = originalDisabled
          confirmButton.textContent = originalText
        }
      }
      
      // Add event listener for our handler with high priority (capture phase)
      confirmButton.addEventListener('click', handleConfirmClick, true)
      
      // Store cleanup function for this button
      const cleanup = () => {
        confirmButton.removeEventListener('click', handleConfirmClick, true)
        confirmButton.removeAttribute('data-confirm-intercepted')
        if (originalOnClick) {
          confirmButton.onclick = originalOnClick
        }
      }
      
      // Store cleanup function on the button for later use
      confirmButton._confirmCleanup = cleanup
    }

    // Initial setup
    setupConfirmOrderInterception()
    
    // Watch for dynamically added confirm order buttons
    const buttonObserver = new MutationObserver((mutations) => {
      let shouldRecheck = false
      
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              // Check if the added node is or contains the confirm order button
              if (node.matches && node.matches('.el-dialog__body .buy_now_cover .confirm_order_btn')) {
                shouldRecheck = true
              } else if (node.querySelector && node.querySelector('.el-dialog__body .buy_now_cover .confirm_order_btn')) {
                shouldRecheck = true
              }
            }
          })
        }
      })
      
      if (shouldRecheck) {
        console.log('New confirm order button detected, setting up interception...')
        setTimeout(setupConfirmOrderInterception, 100) // Small delay to ensure DOM is ready
      }
    })
    
    // Start observing the document for changes
    buttonObserver.observe(document.body, {
      childList: true,
      subtree: true
    })
    
    // Also set up a periodic check in case the button appears without triggering mutations
    const periodicCheck = setInterval(() => {
      setupConfirmOrderInterception()
    }, 1000)
    
    // Cleanup function
    return () => {
      console.log('Cleaning up confirm order button interceptor...')
      buttonObserver.disconnect()
      clearInterval(periodicCheck)
      
      // Clean up all intercepted buttons
      const interceptedButtons = document.querySelectorAll('button[data-confirm-intercepted="true"]')
      interceptedButtons.forEach(button => {
        if (button._confirmCleanup) {
          button._confirmCleanup()
        }
      })
    }
  }
})