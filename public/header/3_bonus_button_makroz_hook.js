// Bonus Button Ma<PERSON>z Hook - Intercepts bonus demand buttons and sends requests to our backend
window.$HEADER.onLifecycle({
  watch: 'div[class*="bonustracker_promo_content"]',
  selector: 'body',
  onMount: (_, watchContainer, kill) => {
    console.log('Bonus tracker promo content detected, setting up button watcher...')

    // Function to fetch promotion settings from API
    async function fetchPromotionSettings() {
      try {
        console.log('Fetching promotion settings...')
        const response = await fetch('https://pn17.pfnow100.com/api/tr/promoTracker/settings', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json'
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const data = await response.json()
        console.log('Promotion settings fetched:', data)
        return data
      } catch (error) {
        console.error('Error fetching promotion settings:', error)
        throw error
      }
    }

    // Function to find promotion ID by title
    function findPromotionIdByTitle(promotions, selectedTitle) {
      if (!promotions || !Array.isArray(promotions)) {
        console.error('Invalid promotions data')
        return null
      }

      const promotion = promotions.find(promo => promo.title === selectedTitle)
      if (promotion) {
        console.log(`Found promotion: ${promotion.title} with ID: ${promotion.id}`)
        return promotion.id
      } else {
        console.error(`Promotion not found for title: ${selectedTitle}`)
        return null
      }
    }

    // Function to send bonus request to our backend
    async function sendBonusRequest() {
      try {
        console.log('Sending bonus request to backend...')

        // Get the selected promotion title from the bonustracker input
        const bonusTrackerInput = document.querySelector('.bonustracker input.el-input__inner')
        if (!bonusTrackerInput) {
          console.error('Bonus tracker input not found')
          showNotification('Bonus seçimi bulunamadı. Lütfen bir bonus seçin.', 'error')
          return false
        }

        const selectedTitle = bonusTrackerInput.value
        if (!selectedTitle) {
          console.error('No promotion selected')
          showNotification('Lütfen bir bonus seçin.', 'error')
          return false
        }

        console.log(`Selected promotion title: ${selectedTitle}`)

        // Fetch promotion settings to get the promotion ID
        const promotionSettings = await fetchPromotionSettings()
        const promotionId = findPromotionIdByTitle(promotionSettings.promotions, selectedTitle)

        if (!promotionId) {
          showNotification('Seçilen bonus bulunamadı. Lütfen tekrar deneyin.', 'error')
          return false
        }

        // Get customer code from localStorage
        const customerCode = window.localStorage.getItem('customerCode')
        if (!customerCode) {
          console.error('Customer code not found in localStorage')
          showNotification('Müşteri kodu bulunamadı. Lütfen giriş yapın.', 'error')
          return false
        }

        // Get auth token from localStorage and clean it
        const authToken = window.localStorage.getItem('s7oryO9STV')?.replace(/^"|"$/g, '') || ''

        const requestBody = {
          id: promotionId,
          type: 1,
          token: authToken,
          code: customerCode,
          metadata: {
            bonus_name: selectedTitle
          }
        }

        console.log('Sending request body:', requestBody)

        const response = await fetch(window.$ENVIRONMENT.API_ENDPOINTS.BONUS_REQUESTS, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(requestBody)
        })

        if (response.ok) {
          const result = await response.json()
          console.log('Bonus request sent successfully:', result)

          // Show success notification
          showNotification('Bonus talebiniz başarıyla gönderildi! En kısa sürede işleme alınacaktır.', 'success')
          return true
        } else {
          console.error('Failed to send bonus request:', response.status)
          showNotification('Bonus talebi gönderilirken bir hata oluştu. Lütfen tekrar deneyin.', 'error')
          return false
        }
      } catch (error) {
        console.error('Error sending bonus request:', error)
        showNotification('Bonus talebi gönderilirken bir hata oluştu. Lütfen tekrar deneyin.', 'error')
        return false
      }
    }

    // Function to show notifications (copied from call request implementation)
    function showNotification(message, type = 'info') {
      // Remove existing notifications
      const existingNotifications = document.querySelectorAll('.bonus-notification')
      existingNotifications.forEach(notification => notification.remove())

      // Create notification element
      const notification = document.createElement('div')
      notification.className = `bonus-notification bonus-notification-${type}`
      notification.textContent = message

      // Style the notification
      Object.assign(notification.style, {
        position: 'fixed',
        top: '20px',
        right: '20px',
        padding: '12px 20px',
        borderRadius: '4px',
        color: 'white',
        fontWeight: 'bold',
        zIndex: '10000',
        maxWidth: '300px',
        wordWrap: 'break-word',
        backgroundColor: type === 'success' ? '#4CAF50' : '#f44336',
        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
      })

      document.body.appendChild(notification)

      // Auto remove after 5 seconds
      setTimeout(() => {
        if (notification.parentNode) {
          notification.remove()
        }
      }, 5000)
    }

    // Function to find and intercept bonus buttons
    function setupBonusButtonInterception() {
      // Look for buttons with "el-button--demandbonus" in their class name
      const bonusButtons = watchContainer.querySelectorAll('button[class*="el-button--demandbonus"]')

      console.log(`Found ${bonusButtons.length} bonus demand buttons`)

      bonusButtons.forEach((button, index) => {
        // Check if we've already processed this button
        if (button.hasAttribute('data-bonus-intercepted')) {
          return
        }

        console.log(`Setting up interception for bonus button ${index + 1}`)

        // Mark button as processed
        button.setAttribute('data-bonus-intercepted', 'true')

        // Store original onclick handler if it exists
        const originalOnClick = button.onclick

        // Remove original onclick handler
        button.onclick = null

        // Add our custom click handler
        const handleBonusClick = async e => {
          e.preventDefault()
          e.stopPropagation()

          console.log('Bonus button clicked, intercepting...')

          // Disable button temporarily to prevent double clicks
          const originalText = button.textContent
          button.disabled = true
          button.textContent = 'Gönderiliyor...'

          try {
            const success = await sendBonusRequest()

            if (success) {
              // Keep button disabled for a few seconds after success
              setTimeout(() => {
                button.disabled = false
                button.textContent = originalText
              }, 3000)
            } else {
              // Re-enable button immediately on failure
              button.disabled = false
              button.textContent = originalText
            }
          } catch (error) {
            console.error('Error in bonus button handler:', error)
            button.disabled = false
            button.textContent = originalText
          }
        }

        // Add event listener for our handler
        button.addEventListener('click', handleBonusClick)

        // Store cleanup function for this button
        const cleanup = () => {
          button.removeEventListener('click', handleBonusClick)
          button.removeAttribute('data-bonus-intercepted')
          if (originalOnClick) {
            button.onclick = originalOnClick
          }
        }

        // Store cleanup function on the button for later use
        button._bonusCleanup = cleanup
      })
    }

    // Initial setup
    setupBonusButtonInterception()

    // Watch for dynamically added bonus buttons
    const buttonObserver = new MutationObserver(mutations => {
      let shouldRecheck = false

      mutations.forEach(mutation => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach(node => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              // Check if the added node contains bonus buttons
              if (node.matches && node.matches('button[class*="el-button--demandbonus"]')) {
                shouldRecheck = true
              } else if (node.querySelector && node.querySelector('button[class*="el-button--demandbonus"]')) {
                shouldRecheck = true
              }
            }
          })
        }
      })

      if (shouldRecheck) {
        console.log('New bonus buttons detected, setting up interception...')
        setupBonusButtonInterception()
      }
    })

    // Start observing the bonus tracker container for changes
    buttonObserver.observe(watchContainer, {
      childList: true,
      subtree: true
    })

    // Cleanup function
    return () => {
      console.log('Cleaning up bonus button watcher...')
      buttonObserver.disconnect()

      // Clean up all intercepted buttons
      const interceptedButtons = watchContainer.querySelectorAll('button[data-bonus-intercepted="true"]')
      interceptedButtons.forEach(button => {
        if (button._bonusCleanup) {
          button._bonusCleanup()
        }
      })
    }
  }
})
