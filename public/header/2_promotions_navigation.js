const TABS = ['all', 'welcome', 'investment', 'loss', 'sport', 'casino', 'special', 'tournaments']

window.$HEADER.onLifecycle({
  page: /^\/\w+\/contents\/promotions/,
  selector: `.blog_types`,
  onMount: (container, _, kill) => {
    const tabName = new URLSearchParams(window.location.search).get('tab')
    const index = TABS.indexOf(tabName.toLowerCase())
    if (index > 0) {
      container.childNodes[index]?.click()
    }

    for (let i = 0; i < container.childNodes.length; i++) {
      const link = container.childNodes[i]
      link.addEventListener('click', () => {
        const url = new URL(window.location.href)
        url.searchParams.set('tab', TABS[i])
        window.history.pushState({}, '', url.toString())
      })
    }
  }
})
