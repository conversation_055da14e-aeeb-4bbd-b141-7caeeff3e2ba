const headerHTML = `
<!-- Header Section -->
<header class="makrobet-header">
    <div class="header-container">
        <!-- Logo -->
        <div class="header-logo">
            <a href="/" class="logo-link" aria-label="Makrobet Ana Sayfa">
                <img src="/views/trader/makrobet/assets/images/logo.png" alt="Makrobet" class="logo-image">
            </a>
        </div>

        <!-- Navigation Menu -->
        <div class="header-nav">
            <ul class="nav-menu" id="nav-menu">
                <!-- Menu items will be generated by JavaScript -->
            </ul>

            <!-- Search Container -->
            <div class="header-search-container">
                <div class="search-input-wrapper">
                    <input type="text" id="game-search-input" class="game-search-input browser-default" placeholder="Oyun ara..." autocomplete="off">
                </div>
                <div class="search-results" id="search-results">
                    <!-- Search results will be populated here -->
                </div>
            </div>

            <!-- Mobile Menu Toggle -->
            <button class="mobile-menu-toggle" id="mobile-menu-toggle" aria-label="Menüyü aç/kapat">
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
            </button>
        </div>
    </div>
</header>
`

// Header Navigation JavaScript
window.$HEADER.onLifecycle({
  selector: `#menu-wrapper`,
  onMount: (container, _, kill) => {
    container.style.display = 'none'
    const div = document.createElement('div')
    div.innerHTML = headerHTML
    container.parentElement.appendChild(div)

    window.addEventListener(
      '@makrobet/unload/header',
      () => {
        container.style.display = 'block'
        div.remove()
        kill()
      },
      { once: true }
    )

    // Provider cache configuration
    const PROVIDER_CACHE_KEY = `$header_providers`
    const PROVIDER_CACHE_TIMESTAMP_KEY = `$header_providers_timestamp`
    const PROVIDER_CACHE_DURATION = 3 * 60 * 60 * 1000 // 3 hours in milliseconds
    const PROVIDERS_API_URL = `${window.origin}/odin/api/user/casinoapi/getReservedVendors/ordered`

    // Function to fetch providers from API
    async function fetchProviders() {
      try {
        // Check cache first
        const cachedData = localStorage.getItem(PROVIDER_CACHE_KEY)
        const cachedTimestamp = localStorage.getItem(PROVIDER_CACHE_TIMESTAMP_KEY)
        const now = Date.now()

        if (cachedData && cachedTimestamp && now - parseInt(cachedTimestamp) < PROVIDER_CACHE_DURATION) {
          return JSON.parse(cachedData)
        }

        // Fetch from API
        const response = await fetch(PROVIDERS_API_URL, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            requestBody: {
              currencyId: 1
            },
            languageId: 1,
            device: 'd'
          })
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const data = await response.json()

        if (data.success && data.data && data.data.vendors) {
          const providers = data.data.vendors

          // Cache the data
          localStorage.setItem(PROVIDER_CACHE_KEY, JSON.stringify(providers))
          localStorage.setItem(PROVIDER_CACHE_TIMESTAMP_KEY, now.toString())

          return providers
        } else {
          throw new Error('Invalid response format')
        }
      } catch (error) {
        console.error('Error fetching providers:', error)
        // Return fallback providers
        return [
          { id: 'pragmatic', name: 'Pragmatic Play', code: 'pragmatic' },
          { id: 'evolution', name: 'Evolution Gaming', code: 'evolution' },
          { id: 'netent', name: 'NetEnt', code: 'netent' },
          { id: 'microgaming', name: 'Microgaming', code: 'microgaming' }
        ]
      }
    }

    // Function to create provider menu items from API data
    function createProviderItems(providers) {
      if (!Array.isArray(providers) || providers.length === 0) {
        return []
      }

      return providers
        .filter(item => item.freePlay)
        .map(provider => ({
          link: `/games/casino?provider=${encodeURIComponent(provider.name.toLowerCase().trim())}`,
          onClick: () => {
            window.dispatchEvent(
              new CustomEvent('@makroz/casino/provider_select', { detail: provider.name.toLowerCase().trim() })
            )
          },
          label: provider.name || 'Unknown Provider',
          icon: `//v3.pro1staticserv.com/common/assets/images/casino/22x22/${provider.code || provider.id}.png`
        }))
        .filter(item => item.link && item.label)
    }

    // Menu data with grouped structure (will be populated with dynamic providers)
    let MENU_ITEMS = [
      {
        label: 'SPOR',
        groups: [
          {
            title: 'Bahis Türleri',
            items: [
              {
                link: '/bet/main',
                label: 'Spor Bahisleri',
                icon: 'https://cdn.makroz.org/makrobet/icons/header/sports.png'
              },
              {
                link: '/bet/live',
                label: 'Canlı Bahis',
                icon: 'https://cdn.makroz.org/makrobet/icons/header/live_sports.png'
              },
              {
                link: '/bet/ultraplay-esports',
                label: 'E-Sporlar',
                icon: 'https://cdn.makroz.org/makrobet/icons/header/e_sports.png'
              }
            ]
          }
        ]
      },
      {
        label: 'CASİNO',
        groups: [
          {
            seeMoreLink: '/games/casino',
            seeMoreLabel: 'Tüm Oyunlar',
            title: 'Oyun Türleri',
            items: [
              {
                link: '/games/casino',
                label: 'Slotlar',
                icon: 'https://cdn.makroz.org/makrobet/icons/header/casino.png'
              },
              {
                link: '/games/livecasino',
                label: 'Canlı Casino',
                icon: 'https://cdn.makroz.org/makrobet/icons/header/live_casino.png'
              }
              // {
              //   link: '/games/poker',
              //   label: 'Poker',
              //   icon: 'http://d1htdzjcfc577a.cloudfront.net/icons/poker-min.png'
              // }
            ]
          },
          {
            title: 'Sağlayıcılar',
            items: [] // Will be populated dynamically
          }
        ]
      },
      // {
      //   "label": "TRADING",
      //   "groups": [
      //     {
      //       "title": "Vadeli İşlemler",
      //       "items": [
      //         {
      //           "link": "/games/livecasino",
      //           "label": "Kripto Vadeli İşlemler",
      //           "icon": "https://cdn.makroz.org/makrobet/icons/crypto_futures-min.png"
      //         },
      //         {
      //           "link": "/games/casino",
      //           "label": "Forex Vadeli İşlemler",
      //           "icon": "https://cdn.makroz.org/makrobet/icons/forex_futures-min.png"
      //         }
      //       ]
      //     }
      //   ]
      // },
      {
        label: 'BONUSLAR',
        groups: [
          {
            title: 'Bonuslar',
            seeMoreLink: '/contents/promotions',
            seeMoreLabel: 'Tüm Bonuslar',
            items: [
              {
                link: '/contents/promotions?tab=investment',
                label: 'Yatırım Bonusları',
                icon: 'https://cdn.makroz.org/makrobet/icons/header/invest_bonus.png'
              },
              {
                link: '/contents/promotions?tab=loss',
                label: 'Kayıp Bonusları',
                icon: 'https://cdn.makroz.org/makrobet/icons/header/loss_bonus.png'
              },
              {
                link: '/contents/promotions?tab=special',
                label: 'Özel Bonuslar',
                icon: 'https://cdn.makroz.org/makrobet/icons/header/special_bonus.png'
              }
            ]
          },
          {
            title: 'Promosyonlar',
            seeMoreLink: '/contents/promotions',
            seeMoreLabel: 'Tüm Promosyonlar',
            items: [
              {
                link: '/contents/promotions',
                label: 'Güncel Promosyonlar',
                icon: 'https://cdn.makroz.org/makrobet/icons/header/promotions.png'
              }
            ]
          }
        ]
      },
      {
        label: 'TURNUVALAR',
        groups: [
          {
            title: 'Turnuvalar',
            seeMoreLink: '/pages/tournaments',
            seeMoreLabel: 'Tüm Turnuvalar',
            items: [],
            customRender: renderTournamentItem
          }
        ]
      },
      {
        label: 'TOMBALA OYUNLAR',
        groups: [
          {
            title: 'Tombala Oyunlar',
            seeMoreLink: '/games/livecasino',
            seeMoreLabel: 'Tüm Oyunlar',
            items: [],
            customRender: renderTombalaItem
          }
        ]
      }
    ]

    // Tournament API configuration
    const TOURNAMENTS_API = 'https://pn17.pfnow100.com/api/tr/tournaments'
    const TOURNAMENT_SLIDER_API = 'https://pn17.pfnow100.com/api/tr/consumer'
    const IMAGE_BASE_URL = window.origin

    // Custom render function for tournament items
    function renderTournamentItem(child, childItem) {
      const childLink = document.createElement('a')
      childLink.href = window.$HEADER.getLang() + child.link
      childLink.className = 'mega-menu-link tournament-item'
      childLink.onclick = e => {
        e.preventDefault()
        window.$HEADER.navigate(child.link)
      }

      // Create tournament card container
      const tournamentCard = document.createElement('div')
      tournamentCard.className = 'tournament-card'

      // Add tournament image
      if (child.image) {
        const tournamentImage = document.createElement('img')
        tournamentImage.src = child.image
        tournamentImage.className = 'tournament-image'
        tournamentImage.alt = child.label
        tournamentImage.onerror = function () {
          this.style.display = 'none'
        }
        tournamentCard.appendChild(tournamentImage)
      }

      // No text overlay - just the image

      childLink.appendChild(tournamentCard)
      childItem.appendChild(childLink)

      return childItem
    }

    // Custom render function for tombala items
    function renderTombalaItem(child, childItem) {
      const childLink = document.createElement('a')
      childLink.href = window.$HEADER.getLang() + child.link
      childLink.className = 'mega-menu-link tombala-item'
      childLink.onclick = e => {
        e.preventDefault()
        window.$HEADER.navigate(child.link)
      }

      // Create tombala card container
      const tombalaCard = document.createElement('div')
      tombalaCard.className = 'tombala-card'

      // Add game image
      if (child.image) {
        const gameImage = document.createElement('img')
        gameImage.src = child.image
        gameImage.className = 'tombala-image'
        gameImage.alt = child.label
        gameImage.onerror = function () {
          this.style.display = 'none'
        }
        tombalaCard.appendChild(gameImage)
      }

      // Add game name
      const gameName = document.createElement('div')
      gameName.className = 'tombala-name'
      gameName.textContent = child.label
      tombalaCard.appendChild(gameName)

      childLink.appendChild(tombalaCard)
      childItem.appendChild(childLink)

      return childItem
    }

    // Function to parse custom class data
    function parseCustomClass(customClass) {
      if (!customClass) return {}

      try {
        const pairs = customClass.split(',')
        const result = {}
        pairs.forEach(pair => {
          const [key, value] = pair.split(':')
          if (key && value) {
            result[key.trim()] = value.trim()
          }
        })
        return result
      } catch (error) {
        console.error('Error parsing custom class:', error)
        return {}
      }
    }

    // Function to fetch tournaments
    async function fetchTournaments() {
      try {
        console.log('Fetching tournaments for header menu...')

        // Fetch tournament slides and tournament data in parallel
        const [slidesResponse, tournamentsResponse] = await Promise.all([
          fetch(TOURNAMENT_SLIDER_API),
          fetch(TOURNAMENTS_API)
        ])

        if (!slidesResponse.ok || !tournamentsResponse.ok) {
          throw new Error('Failed to fetch tournament data')
        }

        // const slidesData = await slidesResponse.json()
        const tournamentsData = await tournamentsResponse.json()

        // Filter slides for tournament type (assuming type 37 based on tournaments.js)
        // const tournamentSlides = slidesData.filter(slide => slide.type === 37 && slide.status === 1)

        // Map slides to tournament data
        // const tournaments = tournamentSlides
        //   .map(slide => {
        //     const customData = parseCustomClass(slide.custom_class)

        //     const tournamentData = tournamentsData.find(
        //       t => t.id.toString() === customData.id && t.end_date > Math.floor(Date.now() / 1000)
        //     )

        //     if (!tournamentData) return null

        //     return {
        //       label: tournamentData.name || 'Tournament',
        //       link: `/tournaments/${tournamentData.id}`,
        //       image: slide.path.startsWith('http') ? slide.path : IMAGE_BASE_URL + slide.path,
        //       id: tournamentData.id
        //     }
        //   })
        //   .filter(Boolean)
        const tournaments = tournamentsData
          .filter(d => d.start_date * 1000 <= Date.now() && d.end_date * 1000 >= Date.now())
          .map(data => {
            return {
              label: data.name || 'Tournament',
              link: `/pages/tournaments`,
              image: data.thumb.startsWith('http')
                ? data.thumb
                : data.thumb.startsWith('/')
                ? data.thumb
                : 'https://cdn.pfcdnserv.com/merchants/pn17/uploads/' + data.thumb,
              id: data.id
            }
          })
        console.log(tournamentsData)

        // Update the tournaments menu items
        const tournamentsMenu = MENU_ITEMS.find(item => item.label === 'TURNUVALAR')
        if (tournamentsMenu && tournamentsMenu.groups[0]) {
          tournamentsMenu.groups[0].items = tournaments.slice(0, 8) // Limit to 8 items
          console.log(`Loaded ${tournaments.length} tournaments for header menu`)
        }

        // Re-render the menu if it's already been rendered
        if (document.querySelector('.makrobet-header .nav-menu')) {
          renderMenu()
        }
      } catch (error) {
        console.error('Error fetching tournaments for header:', error)
      }
    }

    // Function to fetch tombala games
    async function fetchTombalaGames() {
      try {
        console.log('Fetching tombala games for header menu...')

        // Target game IDs for tombala
        const targetGameIds = [17163, 17164, 1196, 20752]

        // Fetch live casino games
        const response = await fetch(`${window.origin}/odin/api/user/casinoapi/getReservedGames`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            requestBody: {
              currencyId: 1,
              gameType: 'liveCasino'
            },
            languageId: 1,
            device: 'd'
          })
        })

        if (!response.ok) {
          throw new Error('Failed to fetch live casino games')
        }

        const data = await response.json()
        const liveGames = Array.isArray(data.data?.games) ? data.data.games : []

        // Filter games by target IDs
        console.log(liveGames)
        const tombalaGames = liveGames
          .filter(game => targetGameIds.includes(parseInt(game.id)))
          .map(game => ({
            label: game.name || 'Tombala Game',
            link: `/games/livecasino/detail/normal/${game.id}/${game.vendorLimitGroups?.[0]?.vendorLimitId}`,
            image: getGameImageUrl(game.id),
            id: game.id
          }))

        // Update the tombala menu items
        const tombalaMenu = MENU_ITEMS.find(item => item.label === 'TOMBALA OYUNLAR')
        if (tombalaMenu && tombalaMenu.groups[0]) {
          tombalaMenu.groups[0].items = tombalaGames
          console.log(`Loaded ${tombalaGames.length} tombala games for header menu`)
        }

        // Re-render the menu if it's already been rendered
        if (document.querySelector('.makrobet-header .nav-menu')) {
          renderMenu()
        }
      } catch (error) {
        console.error('Error fetching tombala games for header:', error)
      }
    }

    // Function to get game image URL
    function getGameImageUrl(gameId) {
      if (!gameId) {
        return `${window.location.origin}/cdn/common/assets/images/livecasino/default.jpg`
      }
      return `${window.location.origin}/cdn/common/assets/images/livecasino/300x200/${gameId}.jpg`
    }

    const logo = document.querySelector('.makrobet-header .logo-link')
    logo.onclick = e => {
      e.preventDefault()
      window.$HEADER.navigate('')
    }

    // DOM Elements
    const navMenu = document.getElementById('nav-menu')
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle')

    // State
    let visibleItems = []
    let overflowItems = []

    // Create menu item element with grouped structure
    function createMenuItem(item, isOverflow = false) {
      const li = document.createElement('li')
      li.className = isOverflow ? 'dropdown-item' : 'nav-item'

      if (item.groups && item.groups.length > 0) {
        // Item with grouped submenu
        const linkClass = isOverflow ? 'dropdown-link has-children' : 'nav-link has-children'

        // Create the main link
        const mainLink = document.createElement('a')
        mainLink.href = '#'
        mainLink.className = linkClass
        mainLink.setAttribute('data-label', item.label)
        mainLink.textContent = item.label

        // Create submenu with container for full-screen layout
        const submenu = document.createElement('ul')
        submenu.className = 'dropdown-menu mega-menu'

        // Create container for proper layout
        const container = document.createElement('div')
        container.className = 'mega-menu-container'

        // Add groups to submenu in 2-column layout
        const groupsGrid = document.createElement('div')
        groupsGrid.className = 'mega-menu-groups-grid'

        item.groups.forEach((group, groupIndex) => {
          const groupSection = document.createElement('div')
          groupSection.className = 'mega-menu-group'
          groupSection.setAttribute('data-group-index', groupIndex)

          // Create group header with navigation controls
          const groupHeader = document.createElement('div')
          groupHeader.className = 'mega-menu-group-header'

          const titleElement = document.createElement('h3')
          titleElement.className = 'mega-menu-group-title'
          titleElement.textContent = group.title
          groupHeader.appendChild(titleElement)

          // Add navigation controls container
          const navControls = document.createElement('div')
          navControls.className = 'mega-menu-nav-controls'

          // Check if group has more than 4 items for pagination
          if (group.items.length > 4) {
            const prevButton = document.createElement('button')
            prevButton.className = 'mega-menu-nav-btn mega-menu-prev'
            prevButton.innerHTML = '‹'
            prevButton.setAttribute('aria-label', 'Previous page')

            const nextButton = document.createElement('button')
            nextButton.className = 'mega-menu-nav-btn mega-menu-next'
            nextButton.innerHTML = '›'
            nextButton.setAttribute('aria-label', 'Next page')

            navControls.appendChild(prevButton)
            navControls.appendChild(nextButton)
          }

          // Check if group has "seeMoreLink" for "See All" button
          if (group.seeMoreLink) {
            const seeAllButton = document.createElement('a')
            seeAllButton.className = 'mega-menu-see-all'
            seeAllButton.textContent = group.seeMoreLabel || 'See All'
            seeAllButton.href = window.$HEADER.getLang() + group.seeMoreLink
            seeAllButton.onclick = e => {
              e.preventDefault()
              window.$HEADER.navigate(group.seeMoreLink)
            }
            navControls.appendChild(seeAllButton)
          }

          if (navControls.children.length > 0) {
            groupHeader.appendChild(navControls)
          }

          // Create group content (always visible)
          const groupContent = document.createElement('div')
          groupContent.className = 'mega-menu-group-content'

          // Add items to group content in 2x2 grid with pagination
          const itemsGrid = document.createElement('div')
          itemsGrid.className = 'mega-menu-items-grid'

          // Initialize pagination state
          let currentPage = 0
          const itemsPerPage = 4

          function renderPage(pageIndex) {
            itemsGrid.innerHTML = ''
            const startIndex = pageIndex * itemsPerPage
            const endIndex = Math.min(startIndex + itemsPerPage, group.items.length)

            for (let i = startIndex; i < endIndex; i++) {
              const child = group.items[i]
              const childItem = document.createElement('div')
              childItem.className = 'mega-menu-item'

              // Use custom render function if available
              if (group.customRender && typeof group.customRender === 'function') {
                group.customRender(child, childItem)
              } else {
                // Default rendering
                const childLink = document.createElement('a')
                childLink.href = window.$HEADER.getLang() + child.link
                childLink.className = 'mega-menu-link'
                childLink.onclick = e => {
                  e.preventDefault()
                  window.$HEADER.navigate(child.link)
                  if (child.onClick) {
                    child.onClick()
                  }
                }

                // Add icon if available
                if (child.icon) {
                  const icon = document.createElement('img')
                  icon.src = child.icon
                  icon.className = 'mega-menu-icon'
                  icon.alt = child.label
                  childLink.appendChild(icon)
                }

                // Add text label
                const textSpan = document.createElement('span')
                textSpan.className = 'mega-menu-text'
                textSpan.textContent = child.label
                childLink.appendChild(textSpan)

                childItem.appendChild(childLink)
              }

              itemsGrid.appendChild(childItem)
            }

            // Update navigation button states
            const prevBtn = groupSection.querySelector('.mega-menu-prev')
            const nextBtn = groupSection.querySelector('.mega-menu-next')
            if (prevBtn) prevBtn.disabled = pageIndex === 0
            if (nextBtn) nextBtn.disabled = endIndex >= group.items.length
          }

          // Add pagination event listeners
          const prevBtn = navControls.querySelector('.mega-menu-prev')
          const nextBtn = navControls.querySelector('.mega-menu-next')

          if (prevBtn) {
            prevBtn.addEventListener('click', e => {
              e.preventDefault()
              if (currentPage > 0) {
                currentPage--
                renderPage(currentPage)
              }
            })
          }

          if (nextBtn) {
            nextBtn.addEventListener('click', e => {
              e.preventDefault()
              const maxPage = Math.ceil(group.items.length / itemsPerPage) - 1
              if (currentPage < maxPage) {
                currentPage++
                renderPage(currentPage)
              }
            })
          }

          // Render initial page
          renderPage(currentPage)

          groupContent.appendChild(itemsGrid)
          groupSection.appendChild(groupHeader)
          groupSection.appendChild(groupContent)

          groupsGrid.appendChild(groupSection)
        })

        container.appendChild(groupsGrid)

        submenu.appendChild(container)
        li.appendChild(mainLink)
        li.appendChild(submenu)

        // Add click handler for submenu toggle on mobile
        mainLink.addEventListener('click', e => {
          e.preventDefault()
          if (window.innerWidth <= 768 || isOverflow) {
            li.classList.toggle('active')
          }
        })
      } else {
        // Regular item
        const linkClass = isOverflow ? 'dropdown-link' : `nav-link${item.new ? ' new' : ''}`

        const link = document.createElement('a')
        link.href = item.link
        link.className = linkClass
        link.textContent = item.label
        link.onclick = e => {
          e.preventDefault()
          window.$HEADER.navigate(item.link)
        }

        li.appendChild(link)
      }

      return li
    }

    // Create overflow dropdown
    function createOverflowDropdown(items) {
      const li = document.createElement('li')
      li.className = 'nav-item overflow-dropdown'

      // Create the toggle button
      const toggleButton = document.createElement('button')
      toggleButton.className = 'overflow-toggle'
      toggleButton.textContent = `+${items.length}`

      // Create the dropdown menu with container
      const dropdownMenu = document.createElement('ul')
      dropdownMenu.className = 'dropdown-menu mega-menu'

      // Create container for proper layout
      const container = document.createElement('div')
      container.className = 'mega-menu-container'

      // Add each item to the dropdown
      items.forEach(item => {
        const menuItem = createMenuItem(item, true)
        container.appendChild(menuItem)
      })

      dropdownMenu.appendChild(container)
      li.appendChild(toggleButton)
      li.appendChild(dropdownMenu)

      return li
    }

    // Calculate menu layout
    function calculateMenuLayout() {
      if (window.innerWidth <= 768) {
        // Mobile: show all items
        visibleItems = [...MENU_ITEMS]
        overflowItems = []
        return
      }

      // Desktop: calculate based on available width
      const container = document.querySelector('.header-container')
      const logo = document.querySelector('.header-logo')

      if (!container || !logo) {
        // Fallback: show first 8 items, rest in overflow
        visibleItems = MENU_ITEMS.slice(0, 8)
        overflowItems = MENU_ITEMS.slice(8)
        return
      }

      const availableWidth = container.offsetWidth - logo.offsetWidth - 120 // 120px buffer

      // More accurate width estimation
      let currentWidth = 0
      visibleItems = []
      overflowItems = []

      for (let i = 0; i < MENU_ITEMS.length; i++) {
        const item = MENU_ITEMS[i]
        // Better width estimation based on text length and styling
        const baseWidth = item.label.length * 9 // 9px per character
        const padding = 32 // 16px padding each side
        const estimatedWidth = baseWidth + padding

        // Reserve space for overflow button if we're getting close
        const reserveSpace = MENU_ITEMS.length - i > 1 ? 80 : 0

        if (currentWidth + estimatedWidth + reserveSpace < availableWidth) {
          visibleItems.push(item)
          currentWidth += estimatedWidth
        } else {
          overflowItems = MENU_ITEMS.slice(i)
          break
        }
      }

      // Ensure we don't have just one item in overflow
      if (overflowItems.length === 1 && visibleItems.length > 0) {
        const lastVisible = visibleItems.pop()
        overflowItems.unshift(lastVisible)
      }
    }

    // Render menu
    function renderMenu() {
      calculateMenuLayout()

      navMenu.innerHTML = ''

      // Add visible items
      visibleItems.forEach(item => {
        navMenu.appendChild(createMenuItem(item))
      })

      // Add overflow dropdown if needed
      if (overflowItems.length > 0) {
        navMenu.appendChild(createOverflowDropdown(overflowItems))
      }

      console.log(`Menü oluşturuldu: ${visibleItems.length} görünür, ${overflowItems.length} taşma`)
    }

    // Mobile menu toggle
    function setupMobileMenu() {
      mobileMenuToggle.addEventListener('click', () => {
        mobileMenuToggle.classList.toggle('active')
        navMenu.classList.toggle('active')
      })

      // Close mobile menu when clicking outside
      document.addEventListener('click', e => {
        if (!e.target.closest('.header-nav')) {
          mobileMenuToggle.classList.remove('active')
          navMenu.classList.remove('active')
        }
      })
    }

    // Handle window resize
    function handleResize() {
      // Debounce resize events
      clearTimeout(window.headerResizeTimeout)
      window.headerResizeTimeout = setTimeout(() => {
        renderMenu()
      }, 250)
    }

    // Handle scroll for fixed header with optimized performance
    let isScrolling = false
    let lastScrollTop = 0
    let headerFixed = false

    function handleScroll() {
      if (!isScrolling) {
        requestAnimationFrame(updateHeader)
        isScrolling = true
      }
    }

    function updateHeader() {
      const header = document.querySelector('.makrobet-header')
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop
      const scrollThreshold = 240

      // Only update if scroll position changed significantly
      if (Math.abs(scrollTop - lastScrollTop) < 5) {
        isScrolling = false
        return
      }

      if (scrollTop > scrollThreshold && !headerFixed) {
        // Becoming fixed - don't add body padding to prevent jump
        headerFixed = true
        header.classList.add('fixed')
        header.classList.add('slide-in')

        // Remove slide-in class after animation
        setTimeout(() => {
          header.classList.remove('slide-in')
        }, 200)
      } else if (scrollTop <= scrollThreshold && headerFixed) {
        // Becoming static
        headerFixed = false
        header.classList.remove('fixed', 'slide-in')
      }

      lastScrollTop = scrollTop
      isScrolling = false
    }

    // Function to initialize providers and update menu
    async function initializeProviders() {
      try {
        const providers = await fetchProviders()
        const providersOrder = ['pragmatic_play', 'hacksaw', 'egtd', 'egt']
        const sortedProviders = providers.sort((a, b) => (providersOrder.includes(a.code) ? -1 : 1))
        const providerItems = createProviderItems(sortedProviders)

        // Find the providers group in MENU_ITEMS and update it
        const casinoMenu = MENU_ITEMS.find(item => item.label === 'CASINO')
        if (casinoMenu && casinoMenu.groups) {
          const providersGroup = casinoMenu.groups.find(group => group.title === 'Sağlayıcılar')
          if (providersGroup) {
            providersGroup.items = providerItems
            // Re-render the menu with updated providers
            renderMenu()
            console.log(`Header providers loaded: ${providerItems.length} providers`)
          }
        }
      } catch (error) {
        console.error('Error initializing providers:', error)
      }
    }

    // Initialize header
    function initializeHeader() {
      if (!navMenu || !mobileMenuToggle) {
        console.warn('Header elementleri bulunamadı')
        return
      }

      renderMenu()
      setupMobileMenu()

      // Fetch tournaments and tombala games for the menu
      fetchTournaments()
      fetchTombalaGames()

      // Add resize listener
      window.addEventListener('resize', handleResize)
      setTimeout(handleResize, 0)

      // Add optimized scroll listener for fixed header
      window.addEventListener('scroll', handleScroll, { passive: true })

      // Initialize providers asynchronously
      initializeProviders()

      console.log('Header başarıyla başlatıldı')
    }

    // Game Search Functionality
    function initializeGameSearch() {
      const searchInput = document.getElementById('game-search-input')
      const searchResults = document.getElementById('search-results')

      if (!searchInput || !searchResults) {
        console.warn('Search elements not found')
        return
      }

      let games = []
      let searchTimeout
      let isSearchVisible = false

      // API Configuration
      const GAMES_API = `${window.origin}/odin/api/user/casinoapi/getReservedGames`

      // Fetch games function
      async function fetchGames() {
        try {
          console.log('Fetching games for search...')

          const [slotsResponse, liveCasinoResponse] = await Promise.all([
            fetch(GAMES_API, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                requestBody: {
                  currencyId: 1,
                  gameType: 'casino'
                },
                languageId: 1,
                device: 'd'
              })
            }),
            fetch(GAMES_API, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                requestBody: {
                  currencyId: 1,
                  gameType: 'liveCasino'
                },
                languageId: 1,
                device: 'd'
              })
            })
          ])

          if (!slotsResponse.ok || !liveCasinoResponse.ok) {
            throw new Error(`HTTP error! slots: ${slotsResponse.status}, live: ${liveCasinoResponse.status}`)
          }

          const [slotsData, liveCasinoData] = await Promise.all([slotsResponse.json(), liveCasinoResponse.json()])

          const slotsGames = Array.isArray(slotsData.data?.games)
            ? slotsData.data.games.map(g => ({ ...g, isLive: false }))
            : []
          const liveGames = Array.isArray(liveCasinoData.data?.games)
            ? liveCasinoData.data.games.map(g => ({ ...g, isLive: true }))
            : []

          games = [...slotsGames, ...liveGames]
          console.log(`${games.length} games loaded for search`)
        } catch (error) {
          console.error('Error fetching games for search:', error)
          games = []
        }
      }

      // Search function
      function searchGames(query) {
        if (!query || query.length === 0) {
          return []
        }

        const searchTerm = query.toLowerCase().trim()
        return games.filter(game => game.name && game.name.toLowerCase().includes(searchTerm)).slice(0, 100) // Limit to 100 results
      }

      // Render search results
      function renderSearchResults(results) {
        if (results.length === 0) {
          searchResults.innerHTML = '<div class="search-no-results">Oyun bulunamadı</div>'
          return
        }

        const resultsHTML = results
          .map(game => {
            const gameImage = game.isLive
              ? `/cdn/common/assets/images/livecasino/300x200/${game.id}.jpg`
              : `/cdn/common/assets/images/casino/300x200/${game.id}.jpg`

            const gameUrl = `/games/${game.isLive ? 'livecasino' : 'casino'}/detail/normal/${game.id}`

            return `
          <div class="search-result-item" data-game-url="${gameUrl}">
            <img src="${gameImage}" alt="${
              game.name
            }" class="search-result-image" onerror="this.src='/cdn/common/assets/images/game-placeholder.jpg'">
            <div class="search-result-content">
              <div class="search-result-name">${game.name}</div>
              <div class="search-result-type">${game.isLive ? 'Canlı Casino' : 'Slot Oyunu'}</div>
            </div>
          </div>
        `
          })
          .join('')

        searchResults.innerHTML = resultsHTML

        // Add click handlers
        searchResults.querySelectorAll('.search-result-item').forEach(item => {
          item.addEventListener('click', () => {
            const gameUrl = item.getAttribute('data-game-url')
            hideSearchResults()
            clearSearchInput()
            window.$HEADER.navigate(gameUrl)
          })
        })
      }

      // Show/hide search results
      function showSearchResults() {
        if (!isSearchVisible) {
          searchResults.style.display = 'block'
          isSearchVisible = true
          document.addEventListener('click', handleOutsideClick)
        }
      }

      function hideSearchResults() {
        if (isSearchVisible) {
          searchResults.style.display = 'none'
          isSearchVisible = false
          document.removeEventListener('click', handleOutsideClick)
        }
      }

      function clearSearchInput() {
        searchInput.value = ''
      }

      // Handle clicks outside search
      function handleOutsideClick(event) {
        const searchContainer = document.querySelector('.header-search-container')
        if (searchContainer && !searchContainer.contains(event.target)) {
          hideSearchResults()
        }
      }

      // Event listeners
      searchInput.addEventListener('input', e => {
        const query = e.target.value.trim()

        // Clear previous timeout
        clearTimeout(searchTimeout)

        if (query.length === 0) {
          hideSearchResults()
          return
        }

        // Debounce search
        searchTimeout = setTimeout(() => {
          const results = searchGames(query)
          renderSearchResults(results)
          showSearchResults()
        }, 300)
      })

      searchInput.addEventListener('focus', () => {
        if (searchInput.value.trim().length >= 2) {
          showSearchResults()
        }
      })

      // Initialize by fetching games
      fetchGames()
    }

    // Start initialization
    initializeHeader()
    initializeGameSearch()
  }
})
