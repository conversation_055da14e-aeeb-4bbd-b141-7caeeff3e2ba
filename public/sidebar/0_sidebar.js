// Sidebar Navigation JavaScript
window.$SIDEBAR.onLifecycle({
  watch: '.makrobet-sidebar',
  selector: `[contentcode="m_sidebar_top"]`,
  onMount: (container, _, kill) => {
    container.nextElementSibling.style.display = 'none'
    container.nextElementSibling.nextElementSibling.style.display = 'none'

    // Sidebar menu data - matches header but excludes provider and promotions groups
    const SIDEBAR_MENU = [
      {
        label: 'SPOR',
        section: 'spor',
        children: [
          {
            link: '/bet/sports',
            label: 'Spor Bahisleri',
            icon: 'https://cdn.makroz.org/makrobet/icons/header/sports.png'
          },
          {
            link: '/bet/live',
            label: 'Canlı Bahis',
            icon: 'https://cdn.makroz.org/makrobet/icons/header/live_sports.png'
          },
          {
            link: '/bet/ultraplay-esports',
            label: 'E-Sporlar',
            icon: 'https://cdn.makroz.org/makrobet/icons/header/e_sports.png'
          }
        ]
      },
      {
        label: 'CASİNO',
        section: 'casino',
        children: [
          {
            link: '/games/casino',
            label: 'Slotlar',
            icon: 'https://cdn.makroz.org/makrobet/icons/header/casino.png'
          },
          {
            link: '/games/livecasino',
            label: 'Canlı Casino',
            icon: 'https://cdn.makroz.org/makrobet/icons/header/live_casino.png'
          }
          // {
          //   link: '/games/poker',
          //   label: 'Poker',
          //   icon: 'http://d1htdzjcfc577a.cloudfront.net/icons/poker-min.png'
          // }
        ]
      },
      // {
      //   "label": "TRADING",
      //   "section": "trading",
      //   "children": [
      //     {
      //       "link": "/games/livecasino",
      //       "label": "Kripto Vadeli İşlemler",
      //       "icon": "https://cdn.makroz.org/makrobet/icons/crypto_futures-min.png"
      //     },
      //     {
      //       "link": "/games/casino",
      //       "label": "Forex Vadeli İşlemler",
      //       "icon": "https://cdn.makroz.org/makrobet/icons/forex_futures-min.png"
      //     }
      //   ]
      // },
      {
        label: 'BONUSLAR',
        section: 'bonuslar',
        children: [
          {
            link: '/contents/promotions?tab=investment',
            label: 'Yatırım Bonusları',
            icon: 'https://cdn.makroz.org/makrobet/icons/header/invest_bonus.png'
          },
          {
            link: '/contents/promotions?tab=loss',
            label: 'Kayıp Bonusları',
            icon: 'https://cdn.makroz.org/makrobet/icons/header/loss_bonus.png'
          },
          {
            link: '/contents/promotions?tab=special',
            label: 'Özel Bonusları',
            icon: 'https://cdn.makroz.org/makrobet/icons/header/special_bonus.png'
          },
          {
            link: '/contents/promotions',
            label: 'Güncel Promosyonlar',
            icon: 'https://cdn.makroz.org/makrobet/icons/header/promotions.png'
          }
        ]
      },
      {
        label: 'TURNUVALAR',
        section: 'turnuvalar',
        children: []
      },
      {
        label: 'TOMBALA OYUNLAR',
        section: 'tombala oyunlar',
        children: []
      }
    ]

    // API configuration for tournaments and tombala
    const TOURNAMENTS_API = 'https://pn17.pfnow100.com/api/tr/tournaments'
    const GAMES_API = `${window.origin}/odin/api/user/casinoapi/getReservedGames`

    // Function to fetch tournaments for sidebar
    async function fetchTournamentsForSidebar() {
      try {
        console.log('Fetching tournaments for sidebar...')

        const response = await fetch(TOURNAMENTS_API)
        if (!response.ok) {
          throw new Error('Failed to fetch tournaments')
        }

        const tournamentsData = await response.json()

        // Filter active tournaments and map to sidebar format
        const tournaments = tournamentsData
          .filter(d => d.start_date * 1000 <= Date.now() && d.end_date * 1000 >= Date.now())
          .slice(0, 8) // Limit to 8 items
          .map(data => ({
            link: `/pages/tournaments`,
            label: data.name || 'Tournament',
            icon: 'https://cdn.makroz.org/makrobet/icons/header/tournaments.png'
          }))

        // Update the tournaments section
        const tournamentsSection = SIDEBAR_MENU.find(item => item.label === 'TURNUVALAR')
        if (tournamentsSection) {
          tournamentsSection.children = tournaments
          console.log(`Loaded ${tournaments.length} tournaments for sidebar`)
        }

        return tournaments
      } catch (error) {
        console.error('Error fetching tournaments for sidebar:', error)
        return []
      }
    }

    // Function to fetch tombala games for sidebar
    async function fetchTombalaForSidebar() {
      try {
        console.log('Fetching tombala games for sidebar...')

        // Target game IDs for tombala
        const targetGameIds = [17163, 17164, 1196, 20752]

        // Fetch live casino games
        const response = await fetch(GAMES_API, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            requestBody: {
              currencyId: 1,
              gameType: 'liveCasino'
            },
            languageId: 1,
            device: 'd'
          })
        })

        if (!response.ok) {
          throw new Error('Failed to fetch live casino games')
        }

        const data = await response.json()
        const liveGames = Array.isArray(data.data?.games) ? data.data.games : []

        // Filter games by target IDs and map to sidebar format
        const tombalaGames = liveGames
          .filter(game => targetGameIds.includes(parseInt(game.id)))
          .map(game => ({
            link: `/games/livecasino/detail/${game.id}/${game.vendorLimitGroups?.[0]?.vendorLimitId}`,
            label: game.name || 'Tombala Game',
            icon: 'https://cdn.makroz.org/makrobet/icons/header/bingo_games.png'
          }))

        // Update the tombala section
        const tombalaSection = SIDEBAR_MENU.find(item => item.label === 'TOMBALA OYUNLAR')
        if (tombalaSection) {
          tombalaSection.children = tombalaGames
          console.log(`Loaded ${tombalaGames.length} tombala games for sidebar`)
        }

        return tombalaGames
      } catch (error) {
        console.error('Error fetching tombala games for sidebar:', error)
        return []
      }
    }

    // Function to populate dynamic sections
    async function populateDynamicSections() {
      try {
        // Fetch both tournaments and tombala games in parallel
        await Promise.all([fetchTournamentsForSidebar(), fetchTombalaForSidebar()])

        // Re-render sidebar if it's already been rendered
        const sidebarContainer = document.querySelector('.makrobet-sidebar')
        if (sidebarContainer && sidebarContainer.innerHTML.trim()) {
          renderSidebarItems()
        }
      } catch (error) {
        console.error('Error populating dynamic sidebar sections:', error)
      }
    }

    // Detect current language from URL
    const detectCurrentLanguage = () => {
      const path = window.location.pathname
      if (path.startsWith('/en')) return 'en'
      if (path.startsWith('/tr')) return 'tr'
      return 'tr' // Default to Turkish
    }

    // Function to create sidebar item with proper language prefix
    const createSidebarItem = item => {
      const currentLang = detectCurrentLanguage()
      const fullPath = `/${currentLang}${item.link}`
      let isScrolling = false

      const sidebarItem = document.createElement('a')
      sidebarItem.href = fullPath
      sidebarItem.onclick = e => {
        e.preventDefault()
        window.$SIDEBAR.navigate(item.link)
      }
      sidebarItem.ontouchstart = e => {
        isScrolling = false
      }
      sidebarItem.ontouchmove = e => {
        isScrolling = true
      }
      sidebarItem.ontouchend = e => {
        e.preventDefault()

        if (!isScrolling) {
          window.$SIDEBAR.navigate(item.link)
        }
      }
      sidebarItem.className = 'sidebar-item'
      sidebarItem.setAttribute('data-navigate', fullPath)

      const icon = document.createElement('img')
      icon.src = item.icon
      icon.alt = item.label
      icon.className = 'item-icon'

      const label = document.createElement('span')
      label.className = 'item-label'
      label.textContent = item.label

      sidebarItem.appendChild(icon)
      sidebarItem.appendChild(label)

      return sidebarItem
    }

    // Function to render sidebar menu items
    const renderSidebarItems = () => {
      SIDEBAR_MENU.forEach(section => {
        const sectionContent = container.querySelector(`[data-content="${section.section}"]`)
        if (sectionContent) {
          // Clear existing content
          sectionContent.innerHTML = ''

          // Add items to section
          section.children.forEach(item => {
            const sidebarItem = createSidebarItem(item)
            sectionContent.appendChild(sidebarItem)
          })
        }
      })
    }

    // Render sidebar items
    renderSidebarItems()

    // Populate dynamic sections (tournaments and tombala)
    populateDynamicSections()

    // Initialize collapsible sections (all open by default)
    const sections = container.querySelectorAll('.sidebar-section')
    sections.forEach(section => {
      // Sections are open by default, no collapsed class needed
      const header = section.querySelector('.section-header')

      // Add click handler for toggle functionality
      header.addEventListener('click', () => {
        section.classList.toggle('collapsed')
      })
    })

    // Function to setup navigation handlers
    const setupNavigationHandlers = () => {
      const navItems = container.querySelectorAll('.sidebar-item[data-navigate]')

      navItems.forEach(item => {
        let isScrolling = false
        let touchStartY = 0

        const handleNavigation = e => {
          e.preventDefault()

          // Remove active class from all items
          navItems.forEach(navItem => navItem.classList.remove('active'))

          // Add active class to clicked item
          item.classList.add('active')

          // Get navigation path
          const path = item.getAttribute('data-navigate')

          // Use $LANDING.navigate for navigation
          if (window.$LANDING && window.$LANDING.navigate) {
            window.$LANDING.navigate(path)
          } else {
            // Fallback to regular navigation
            window.location.href = path
          }
        }

        item.addEventListener('click', handleNavigation)

        // Add touch support with scroll detection
        item.addEventListener('touchstart', e => {
          isScrolling = false
          touchStartY = e.touches[0].clientY
        })

        item.addEventListener('touchmove', e => {
          const touchMoveY = e.touches[0].clientY
          const deltaY = Math.abs(touchMoveY - touchStartY)

          // If moved more than 10px vertically, consider it scrolling
          if (deltaY > 10) {
            isScrolling = true
          }
        })

        item.addEventListener('touchend', e => {
          // Don't handle touch if user was scrolling
          if (isScrolling) {
            return
          }

          e.preventDefault()
          handleNavigation(e)
        })
      })

      // Set active item based on current path
      const currentPath = window.location.pathname
      navItems.forEach(item => {
        const itemPath = item.getAttribute('data-navigate')
        if (currentPath === itemPath || currentPath.startsWith(itemPath + '/')) {
          item.classList.add('active')
        }
      })

      return navItems
    }

    // Setup navigation after rendering items
    setupNavigationHandlers()

    // Call Me Functionality
    function initializeCallMe() {
      const timeSlotsContainer = container.querySelector('#time-slots-sidebar')
      let isAuthenticated = false
      let authCheckInterval = null
      let availableTimeSlots = []

      // All possible time slots
      const allTimeSlots = [
        { id: 2, time: '11:00 - 12:00' },
        { id: 3, time: '12:00 - 13:00' },
        { id: 4, time: '13:00 - 14:00' },
        { id: 5, time: '14:00 - 15:00' },
        { id: 6, time: '15:00 - 16:00' },
        { id: 7, time: '16:00 - 17:00' },
        { id: 8, time: '17:00 - 18:00' }
      ]

      // Function to get current time and filter available slots
      function getAvailableTimeSlots() {
        return allTimeSlots
      }

      // Function to render available time slots
      function renderTimeSlots() {
        if (!timeSlotsContainer) return

        availableTimeSlots = getAvailableTimeSlots()
        timeSlotsContainer.innerHTML = ''

        if (availableTimeSlots.length === 0) {
          timeSlotsContainer.innerHTML = `
            <div class="no-slots-available">
              Bugün için uygun saat bulunmamaktadır. Yarın tekrar deneyin.
            </div>
          `
          return
        }

        availableTimeSlots.forEach(slot => {
          const timeSlotElement = document.createElement('div')
          timeSlotElement.className = 'time-slot-sidebar'
          timeSlotElement.setAttribute('data-time-id', slot.id)
          timeSlotElement.setAttribute('data-time', slot.time)
          timeSlotElement.textContent = slot.time

          // Add click handler
          timeSlotElement.addEventListener('click', () => handleTimeSlotClick(timeSlotElement))

          timeSlotsContainer.appendChild(timeSlotElement)
        })
      }

      // Check authentication status
      function checkAuthStatus() {
        const token = localStorage.getItem('s7oryO9STV')
        const customerCode = localStorage.getItem('customerCode')
        isAuthenticated = !!(token && customerCode)
        return isAuthenticated
      }

      // Show notification function
      function showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div')
        notification.className = `sidebar-notification ${type}`
        notification.textContent = message
        notification.style.cssText = `
          position: fixed;
          top: 20px;
          right: 20px;
          background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
          color: white;
          padding: 12px 20px;
          border-radius: 8px;
          z-index: 10000;
          font-size: 14px;
          box-shadow: 0 4px 12px rgba(0,0,0,0.3);
          animation: slideInRight 0.3s ease;
        `

        document.body.appendChild(notification)

        // Remove after 3 seconds
        setTimeout(() => {
          notification.style.animation = 'slideOutRight 0.3s ease'
          setTimeout(() => {
            if (notification.parentNode) {
              notification.parentNode.removeChild(notification)
            }
          }, 300)
        }, 3000)
      }

      // Send call request to API
      async function sendCallRequest(timeId, timeText) {
        const token = localStorage.getItem('s7oryO9STV')
        const customerCode = localStorage.getItem('customerCode')

        if (!token || !customerCode) {
          console.error('Missing authentication data')
          return false
        }

        const requestBody = {
          id: parseInt(timeId),
          type: 3,
          token: token,
          code: customerCode,
          time: timeText
        }

        try {
          const response = await fetch(window.$ENVIRONMENT.API_ENDPOINTS.CALL_REQUESTS, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
          })

          if (response.ok) {
            const result = await response.json()
            console.log('Call request sent successfully:', result)
            showNotification('Arama talebiniz başarıyla gönderildi! En kısa sürede sizi arayacağız.', 'success')
            return true
          } else {
            console.error('Failed to send call request:', response.status)
            showNotification('Arama talebi gönderilirken bir hata oluştu. Lütfen tekrar deneyin.', 'error')
            return false
          }
        } catch (error) {
          console.error('Error sending call request:', error)
          showNotification('Arama talebi gönderilirken bir hata oluştu. Lütfen tekrar deneyin.', 'error')
          return false
        }
      }

      // Handle time slot click
      function handleTimeSlotClick(timeSlot) {
        const timeId = timeSlot.getAttribute('data-time-id')
        const timeText = timeSlot.getAttribute('data-time')

        // Remove selected class from all slots
        const currentTimeSlots = timeSlotsContainer.querySelectorAll('.time-slot-sidebar')
        currentTimeSlots.forEach(slot => slot.classList.remove('selected'))

        // Add selected class to clicked slot
        timeSlot.classList.add('selected')

        if (!checkAuthStatus()) {
          document.querySelector('.login-btn')?.click()
          return
        }

        // Send call request
        sendCallRequest(timeId, timeText)
      }

      // Initialize time slots
      renderTimeSlots()

      // Check for pending requests
      function handlePendingCallRequest() {
        const pendingRequest = localStorage.getItem('pendingCallRequest')
        if (pendingRequest && isAuthenticated) {
          try {
            const { timeId, timeText } = JSON.parse(pendingRequest)
            localStorage.removeItem('pendingCallRequest')
            sendCallRequest(timeId, timeText)
          } catch (error) {
            console.error('Error handling pending call request:', error)
            localStorage.removeItem('pendingCallRequest')
          }
        }
      }

      // Start authentication polling
      checkAuthStatus()
      authCheckInterval = setInterval(() => {
        const wasAuthenticated = isAuthenticated
        checkAuthStatus()

        if (!wasAuthenticated && isAuthenticated) {
          handlePendingCallRequest()
        }
      }, 1000)

      // Refresh time slots every 5 minutes to keep them updated
      const timeSlotsRefreshInterval = setInterval(() => {
        renderTimeSlots()
      }, 5 * 60 * 1000)

      // Check for pending requests on initialization
      if (isAuthenticated) {
        handlePendingCallRequest()
      }

      // Return cleanup function
      return () => {
        if (authCheckInterval) {
          clearInterval(authCheckInterval)
        }
        if (timeSlotsRefreshInterval) {
          clearInterval(timeSlotsRefreshInterval)
        }
      }
    }

    // Initialize call me functionality
    const cleanupCallMe = initializeCallMe()

    // Handle language switching
    const languageItems = container.querySelectorAll('.language-item[data-language]')

    // Set current language indicator
    const updateLanguageIndicator = () => {
      const currentLang = detectCurrentLanguage()
      languageItems.forEach(item => {
        const itemLang = item.getAttribute('data-language')
        if (itemLang === currentLang) {
          item.classList.add('current-language')
        } else {
          item.classList.remove('current-language')
        }
      })
    }

    // Initialize language indicator
    updateLanguageIndicator()

    // Add click handlers for language switching
    languageItems.forEach(item => {
      item.addEventListener('click', e => {
        e.preventDefault()

        const targetLang = item.getAttribute('data-language')
        const currentPath = window.location.pathname
        let newPath

        // Remove existing language prefix and add new one
        if (currentPath.startsWith('/en') || currentPath.startsWith('/tr')) {
          // Replace existing language prefix
          newPath = `/${targetLang}${currentPath.substring(3)}`
        } else {
          // Add language prefix to current path
          newPath = `/${targetLang}${currentPath}`
        }

        // Re-render sidebar with new language and navigate
        renderSidebarItems()
        setupNavigationHandlers()
        window.location.href = newPath
      })
    })

    // Function to update sidebar when language changes
    window.updateSidebarLanguage = () => {
      renderSidebarItems()
      setupNavigationHandlers()
      updateLanguageIndicator()
    }

    // Cleanup function
    const cleanup = () => {
      sections.forEach(section => {
        const header = section.querySelector('.section-header')
        header.removeEventListener('click', () => {})
      })

      const currentNavItems = container.querySelectorAll('.sidebar-item[data-navigate]')
      currentNavItems.forEach(item => {
        item.removeEventListener('click', () => {})
      })

      languageItems.forEach(item => {
        item.removeEventListener('click', () => {})
      })

      // Cleanup call me functionality
      if (cleanupCallMe) {
        cleanupCallMe()
      }

      delete window.updateSidebarLanguage
      kill()
    }

    // Listen for unload event
    window.addEventListener('@makrobet/unload/sidebar', cleanup, { once: true })

    return cleanup
  }
})
