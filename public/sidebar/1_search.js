// Sidebar Search JavaScript
window.$SIDEBAR.onLifecycle({
  watch: '.makrobet-sidebar',
  selector: `[contentcode="m_sidebar_top"]`,
  onMount: (container, _, kill) => {
    console.log('Sidebar search mounted')

    // Search functionality
    const searchInput = document.getElementById('sidebar-game-search-input')
    const searchResults = document.getElementById('sidebar-search-results')

    if (!searchInput || !searchResults) {
      console.warn('Sidebar search elements not found')
      return
    }

    let searchTimeout
    let currentSearchTerm = ''

    // Function to get game image URL
    function getGameImageUrl(gameId) {
      if (!gameId) {
        return `${window.location.origin}/cdn/common/assets/images/casino/default.jpg`
      }
      return `${window.location.origin}/cdn/common/assets/images/casino/300x200/${gameId}.jpg`
    }

    // Function to search games
    async function searchGames(query) {
      if (!query) {
        hideSearchResults()
        return
      }

      try {
        console.log('Searching for games:', query)

        // Fetch casino games
        const response = await fetch(`${window.origin}/odin/api/user/casinoapi/getReservedGames`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            requestBody: {
              currencyId: 1,
              gameType: 'casino'
            },
            languageId: 1,
            device: 'd'
          }),
          credentials: 'include'
        })

        if (!response.ok) {
          throw new Error('Failed to fetch games')
        }

        const data = await response.json()
        const games = Array.isArray(data.data?.games) ? data.data.games : []

        // Filter games by search query
        const filteredGames = games
          .filter(game => game.name && game.name.toLowerCase().includes(query.toLowerCase()))
          .slice(0, 8) // Limit to 8 results

        displaySearchResults(filteredGames, query)
      } catch (error) {
        console.error('Error searching games:', error)
        showNoResults()
      }
    }

    // Function to display search results
    function displaySearchResults(games, query) {
      if (games.length === 0) {
        showNoResults()
        return
      }

      const resultsHTML = games
        .map(
          game => `
        <div class="search-result-item" data-game-id="${game.id}" data-game-name="${game.name}">
          <img src="${getGameImageUrl(game.id)}" alt="${
            game.name
          }" class="search-result-image" onerror="this.style.display='none'">
          <div class="search-result-content">
            <div class="search-result-name">${game.name}</div>
            <div class="search-result-type">Casino</div>
          </div>
        </div>
      `
        )
        .join('')

      searchResults.innerHTML = resultsHTML
      showSearchResults()

      // Add click handlers to results
      const resultItems = searchResults.querySelectorAll('.search-result-item')
      resultItems.forEach(item => {
        item.addEventListener('click', () => {
          const gameId = item.getAttribute('data-game-id')
          const gameName = item.getAttribute('data-game-name')

          console.log('Selected game:', gameName, 'ID:', gameId)

          // Navigate to game
          const gameUrl = `/games/casino/detail/normal/${gameId}`
          if (window.$SIDEBAR && window.$SIDEBAR.navigate) {
            window.$SIDEBAR.navigate(gameUrl)
          } else {
            window.location.href = gameUrl
          }

          // Clear search
          searchInput.value = ''
          hideSearchResults()
        })
      })
    }

    // Function to show no results
    function showNoResults() {
      searchResults.innerHTML = '<div class="search-no-results">Oyun bulunamadı</div>'
      showSearchResults()
    }

    // Function to show search results
    function showSearchResults() {
      searchResults.style.display = 'block'
    }

    // Function to hide search results
    function hideSearchResults() {
      searchResults.style.display = 'none'
      searchResults.innerHTML = ''
    }

    // Search input event handlers
    searchInput.addEventListener('input', e => {
      const query = e.target.value.trim()
      currentSearchTerm = query

      // Clear previous timeout
      if (searchTimeout) {
        clearTimeout(searchTimeout)
      }

      // Debounce search
      searchTimeout = setTimeout(() => {
        if (currentSearchTerm === query) {
          searchGames(query)
        }
      }, 300)
    })

    // Hide results when clicking outside
    document.addEventListener('click', e => {
      if (!searchInput.contains(e.target) && !searchResults.contains(e.target)) {
        hideSearchResults()
      }
    })

    // Show results when focusing on input (if there's content)
    searchInput.addEventListener('focus', () => {
      if (searchInput.value.trim().length >= 1) {
        searchGames(searchInput.value.trim())
      }
    })

    // Handle escape key
    searchInput.addEventListener('keydown', e => {
      if (e.key === 'Escape') {
        searchInput.blur()
        hideSearchResults()
      }
    })

    // Cleanup function
    kill(() => {
      if (searchTimeout) {
        clearTimeout(searchTimeout)
      }
      document.removeEventListener('click', hideSearchResults)
    })
  }
})
