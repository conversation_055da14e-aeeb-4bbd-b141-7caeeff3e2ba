/* Center Plus Button */
.radial-button-wrapper {
  position: relative;
  display: inline-block;
  width: 70px;
  height: 70px;
  z-index: 1000;
  padding: 0;
  margin: 0;
  top: -16px;
}

.radial-button {
  position: relative;
  width: 100%;
  height: 100%;
  background: rgb(var(--primary-color));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #16213e;
  font-size: 30px;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 4px 15px rgba(240, 165, 0, 0.5);
  border: none;
  z-index: 2;
}

.radial-button i {
  font-size: 24px;
}

.button-collapse.right-sidebar.coupon {
  display: none !important;
}

.button-collapse.right-sidebar.coupon:hover {
  transform: scale(1.1) !important;
  box-shadow: 0 6px 20px rgba(240, 165, 0, 0.7) !important;
}

.button-collapse.right-sidebar.coupon.active {
  transform: rotate(45deg) !important;
  background: #ff4757 !important;
}

.button-collapse.right-sidebar.coupon i {
  font-size: 30px !important;
  color: #16213e !important;
  line-height: 1 !important;
}

/* Semicircular Menu Container */
.radial-menu-container {
  position: absolute;
  bottom: 56%;
  left: 50%;
  transform: translateX(-50%);
  width: 300px;
  height: 150px;
  pointer-events: none;
  z-index: 1;
  margin-bottom: 10px;
}

.radial-menu-container.active {
  pointer-events: auto;
}

/* SVG Container */
.menu-svg {
  width: 100%;
  height: 100%;
  filter: drop-shadow(0 2px 10px rgba(0, 0, 0, 0.3));
  overflow: visible;
}

/* Menu Sectors */
.menu-sector {
  cursor: pointer;
  /* fill: rgba(15, 52, 96, 0.95); */
  fill: rgba(var(--secondary-color), 0.9);
  stroke: rgba(255, 255, 255, 0.1);
  stroke-width: 1;
  transition: all 0.3s;

  opacity: 0;
  transform: scale(0.1);
  transform-origin: 150px 150px;
}

.radial-menu-container.active .menu-sector {
  opacity: 1;
}

.menu-sector:hover {
  fill: rgba(240, 165, 0, 0.95);
  filter: brightness(1.2);
}

/* Simple fade in animation */
.radial-menu-container.active .menu-sector {
  animation: fadeInSector 0.3s ease-out forwards;
}

.radial-menu-container.active .menu-sector:nth-child(1) {
  animation-delay: 0s;
}

.radial-menu-container.active .menu-sector:nth-child(2) {
  animation-delay: 0.1s;
}

.radial-menu-container.active .menu-sector:nth-child(3) {
  animation-delay: 0.2s;
}

.radial-menu-container.active .menu-sector:nth-child(4) {
  animation-delay: 0.3s;
}

.radial-menu-container.active .menu-sector:nth-child(5) {
  animation-delay: 0.4s;
}

.radial-menu-container.active .menu-sector:nth-child(6) {
  animation-delay: 0.5s;
}

@keyframes fadeInSector {
  from {
    opacity: 0;
    transform: scale(0.1);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Icons and labels */
.sectors-wrapper {
  width: 100%;
  height: 100%;
}

.sector-content {
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  pointer-events: none;
  transform: translate(-50%, -50%);
  text-align: center;
  color: white;
  z-index: 2;
  opacity: 0;
  transition: opacity 0.3s ease-out;
}

.radial-menu-container.active .sector-content:nth-child(1) {
  opacity: 1;
  transition-delay: 0.2s;
}

.radial-menu-container.active .sector-content:nth-child(2) {
  opacity: 1;
  transition-delay: 0.3s;
}

.radial-menu-container.active .sector-content:nth-child(3) {
  opacity: 1;
  transition-delay: 0.4s;
}

.radial-menu-container.active .sector-content:nth-child(4) {
  opacity: 1;
  transition-delay: 0.5s;
}

.radial-menu-container.active .sector-content:nth-child(5) {
  opacity: 1;
  transition-delay: 0.6s;
}

.radial-menu-container.active .sector-content:nth-child(6) {
  opacity: 1;
  transition-delay: 0.7s;
}

.sector-content i {
  font-size: 22px;
  margin-bottom: 4px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.sector-content span {
  font-size: 10px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
  white-space: nowrap;
}
