/* Makrobet Sidebar Styles */
#left-menu {
  padding-bottom: 0;
  display: block !important;
}

.custom_menu_drawer {
  display: none !important;
}

.makrobet-sidebar {
  width: 100%;
  background: rgb(var(--secondary-color));
  border-right: 1px solid rgba(var(--primary-color), 0.1);
}

.makrobet-sidebar .sidebar-container {
  padding: 16px 0;
}

/* Section Styles */
.makrobet-sidebar .sidebar-section {
  /* margin-bottom: 8px; */
}

.makrobet-sidebar .section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 20px;
  background: rgba(var(--primary-color), 0.08);
  border-left: 3px solid rgb(var(--primary-color));
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.makrobet-sidebar .section-header:hover {
  background: rgba(var(--primary-color), 0.12);
}

.makrobet-sidebar .section-title {
  font-size: 0.85rem;
  font-weight: 600;
  color: rgb(var(--primary-color));
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.makrobet-sidebar .section-arrow {
  font-size: 0.7rem;
  color: rgb(var(--primary-color));
  transition: transform 0.2s ease;
}

.makrobet-sidebar .sidebar-section.collapsed .section-arrow {
  transform: rotate(-90deg);
}

/* Section Content */
.makrobet-sidebar .section-content {
  background: rgba(0, 0, 0, 0.1);
  max-height: 500px;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.makrobet-sidebar .sidebar-section.collapsed .section-content {
  max-height: 0;
}

/* Sidebar Items */
.makrobet-sidebar .sidebar-item {
  display: flex;
  align-items: center;
  padding: 14px 24px;
  color: rgba(255, 255, 255, 0.85);
  text-decoration: none;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
  gap: 12px;
}

.makrobet-sidebar .sidebar-item:hover {
  background: rgba(var(--primary-color), 0.08);
  color: rgb(var(--primary-color));
  border-left-color: rgb(var(--primary-color));
}

.makrobet-sidebar .sidebar-item.active {
  background: rgba(var(--primary-color), 0.12);
  color: rgb(var(--primary-color));
  border-left-color: rgb(var(--primary-color));
}

.makrobet-sidebar .item-icon {
  width: 32px;
  height: 32px;
  object-fit: contain;
  flex-shrink: 0;
  opacity: 0.8;
  transition: opacity 0.2s ease;
}

.makrobet-sidebar .sidebar-item:hover .item-icon,
.makrobet-sidebar .sidebar-item.active .item-icon {
  opacity: 1;
}

.makrobet-sidebar .item-label {
  font-size: 1rem;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Language Picker Specific Styles */
.makrobet-sidebar .language-item {
  position: relative;
}

.makrobet-sidebar .language-item.current-language {
  background: rgba(var(--primary-color), 0.15);
  color: rgb(var(--primary-color));
  border-left-color: rgb(var(--primary-color));
}

.makrobet-sidebar .language-item.current-language::after {
  content: '✓';
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  color: rgb(var(--primary-color));
  font-size: 0.8rem;
  font-weight: 600;
}

/* Call Me Sidebar Styles */
.call-me-sidebar-content {
  padding: 16px 20px;
}

.call-me-sidebar-title {
  font-size: 0.8rem;
  font-weight: 600;
  color: rgb(var(--primary-color));
  margin-bottom: 12px;
  text-align: center;
}

.time-slots-sidebar {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.time-slot-sidebar {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 16px;
  background: rgba(var(--primary-color), 0.08);
  border: 1px solid rgba(var(--primary-color), 0.2);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.9);
  text-align: center;
}

.time-slot-sidebar:hover {
  background: rgba(var(--primary-color), 0.15);
  border-color: rgba(var(--primary-color), 0.4);
  color: rgb(var(--primary-color));
  transform: translateX(2px);
}

.time-slot-sidebar:active {
  background: rgba(var(--primary-color), 0.2);
  transform: translateX(1px);
}

.time-slot-sidebar.selected {
  background: rgba(var(--primary-color), 0.2);
  border-color: rgb(var(--primary-color));
  color: rgb(var(--primary-color));
  font-weight: 600;
}

.no-slots-available {
  padding: 16px;
  text-align: center;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.8rem;
  font-style: italic;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .makrobet-sidebar {
    width: 100%;
    height: auto;
  }

  .makrobet-sidebar .section-header {
    padding: 16px 20px;
  }

  .makrobet-sidebar .sidebar-item {
    padding: 16px 20px;
  }

  .makrobet-sidebar .item-label {
    font-size: 0.9rem;
  }
}

/* Scrollbar Styling */
.makrobet-sidebar::-webkit-scrollbar {
  width: 4px;
}

.makrobet-sidebar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
}

.makrobet-sidebar::-webkit-scrollbar-thumb {
  background: rgba(var(--primary-color), 0.3);
  border-radius: 2px;
}

.makrobet-sidebar::-webkit-scrollbar-thumb:hover {
  background: rgba(var(--primary-color), 0.5);
}

/* Notification animations */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }

  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }

  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* Sidebar Search Styles */
.sidebar-search-container {
  position: relative;
  width: 100%;
  margin-bottom: 16px;
  padding: 0 16px;
}

.sidebar-search-container .search-input-wrapper {
  position: relative;
  width: 100%;
}

.sidebar-game-search-input {
  width: 100% !important;
  height: 44px !important;
  margin: 0 !important;
  padding: 12px 16px !important;
  background: rgba(var(--secondary-color), 0.8) !important;
  border: 2px solid rgba(var(--primary-color), 0.3) !important;
  border-radius: 22px !important;
  color: rgba(255, 255, 255, 0.95) !important;
  font-size: 0.9rem !important;
  font-weight: 500 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  outline: none !important;
  backdrop-filter: blur(10px) !important;
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

.sidebar-game-search-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
  font-weight: 400;
}

.sidebar-game-search-input:focus {
  background: rgba(var(--secondary-color), 0.9);
  border-color: rgb(var(--primary-color));
  box-shadow:
    0 0 0 3px rgba(var(--primary-color), 0.15),
    0 8px 24px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.sidebar-game-search-input:hover:not(:focus) {
  background: rgba(var(--secondary-color), 0.85);
  border-color: rgba(var(--primary-color), 0.5);
  transform: translateY(-0.5px);
}

.sidebar-search-results {
  position: absolute;
  top: calc(100% + 8px);
  left: 0;
  right: 0;
  background: linear-gradient(135deg, rgba(20, 20, 30, 0.98) 0%, rgba(15, 15, 25, 0.95) 100%);
  border: 1px solid rgba(var(--primary-color), 0.25);
  border-radius: 12px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.4),
    0 8px 24px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  max-height: 300px;
  overflow-y: auto;
  overflow-x: hidden;
  z-index: 1003;
  display: none;
  animation: searchResultsSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes searchResultsSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.sidebar-search-results .search-result-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.06);
  position: relative;
  overflow: hidden;
}

.sidebar-search-results .search-result-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 0;
  background: linear-gradient(90deg, rgba(var(--primary-color), 0.2) 0%, transparent 100%);
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-search-results .search-result-item:last-child {
  border-bottom: none;
}

.sidebar-search-results .search-result-item:hover {
  background: rgba(var(--primary-color), 0.08);
  transform: translateX(4px);
}

.sidebar-search-results .search-result-item:hover::before {
  width: 4px;
}

.sidebar-search-results .search-result-item:active {
  transform: translateX(2px) scale(0.98);
}

.sidebar-search-results .search-result-image {
  width: 48px;
  height: 32px;
  object-fit: cover;
  border-radius: 6px;
  margin-right: 12px;
  flex-shrink: 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-search-results .search-result-item:hover .search-result-image {
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
}

.sidebar-search-results .search-result-content {
  flex: 1;
  min-width: 0;
}

.sidebar-search-results .search-result-name {
  font-size: 0.85rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.sidebar-search-results .search-result-type {
  font-size: 0.7rem;
  color: rgba(var(--primary-color), 0.8);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.sidebar-search-results .search-no-results {
  padding: 16px;
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.85rem;
}

/* Sidebar Support Links Styles */
.sidebar-support-container {
  padding: 0 16px 16px 16px;
  margin-bottom: 8px;
}

.sidebar-support-links {
  display: flex;
  flex-direction: row;
  gap: 8px;
}

.sidebar-support-link {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 8px !important;
  background: rgba(var(--primary-color), 0.08);
  border: 1px solid rgba(var(--primary-color), 0.2);
  border-radius: 12px;
  color: rgb(var(--primary-color));
  text-decoration: none;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: 500;
}

.sidebar-support-link:hover {
  background: rgba(var(--primary-color), 0.15);
  border-color: rgba(var(--primary-color), 0.3);
  color: rgb(var(--primary-color));
  transform: translateX(4px);
}

.sidebar-support-link i {
  font-size: 1.2rem;
  width: 24px;
  text-align: center;
  flex-shrink: 0;
}

.sidebar-support-text {
  font-weight: 500;
  font-size: 0.7rem;
  letter-spacing: 0.2px;
}

/* WhatsApp specific styling */
.sidebar-support-link[href*="whatsapp"] {
  background: rgba(37, 211, 102, 0.08);
  border-color: rgba(37, 211, 102, 0.2);
  color: rgb(37, 211, 102);
}

.sidebar-support-link[href*="whatsapp"]:hover {
  background: rgba(37, 211, 102, 0.15);
  border-color: rgba(37, 211, 102, 0.3);
  color: rgb(37, 211, 102);
}

/* Telegram specific styling */
.sidebar-support-link[href*="telegram"] {
  background: rgba(0, 136, 204, 0.08);
  border-color: rgba(0, 136, 204, 0.2);
  color: rgb(0, 136, 204);
}

.sidebar-support-link[href*="telegram"]:hover {
  background: rgba(0, 136, 204, 0.15);
  border-color: rgba(0, 136, 204, 0.3);
  color: rgb(0, 136, 204);
}
