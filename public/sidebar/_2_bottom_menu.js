window.$SIDEBAR.onLifecycle({
  watch: '.button-collapse.right-sidebar.coupon',
  selector: `body`,
  onMount: (_, originalButton, kill) => {
    // Create wrapper div
    const wrapper = document.createElement('div')
    wrapper.className = 'radial-button-wrapper'

    const button = document.createElement('div')
    button.className = 'radial-button'
    button.innerHTML = `<i class="fa fa-plus" ></i>`

    // Insert wrapper before button and move button inside
    originalButton.parentNode.insertBefore(wrapper, originalButton)
    // wrapper.appendChild(originalButton)

    // Create radial menu container
    const menuContainer = document.createElement('div')
    menuContainer.className = 'radial-menu-container'
    menuContainer.innerHTML = `
    <svg class="menu-svg" viewBox="0 0 300 150" >
    <path class="menu-sector" data-action="poker" d="M 150 150 L 20.1 75 A 150 150 0 0 0 0 150 Z" />
    <path class="menu-sector" data-action="slots" d="M 150 150 L 75 21.65 A 150 150 0 0 0 20.1 75 Z" />
    <path class="menu-sector" data-action="sports" d="M 150 150 L 150 0 A 150 150 0 0 0 75 21.65 Z" />
    <path class="menu-sector" data-action="bonus" d="M 150 150 L 225 21.65 A 150 150 0 0 0 150 0 Z" />
    <path class="menu-sector" data-action="blackjack" d="M 150 150 L 279.9 75 A 150 150 0 0 0 225 21.65 Z" />
    <path class="menu-sector" data-action="roulette" d="M 150 150 L 300 150 A 150 150 0 0 0 279.9 75 Z" />
    </svg>

    <div class="sectors-wrapper">
      <div class="sector-content" style="left: 40px; top: 117px;" >
        <i class="fa fa-gamepad" ></i> <span>Poker</span>
      </div>

      <div class="sector-content" style="left: 70px; top: 70px;" >
        <i class="fa fa-coins" ></i> <span>Slot</span>
      </div>

      <div class="sector-content" style="left: 123px; top: 41px;" >
        <i class="fa fa-gift" ></i> <span>Bonus</span>
      </div>

      <div class="sector-content" style="left: 180px; top: 41px;" >
        <i class="fa fa-futbol" ></i> <span>Spor</span>
      </div>

      <div class="sector-content" style="left: 230px; top: 70px;" >
        <i class="fa fa-square" ></i> <span>21</span>
      </div>

      <div class="sector-content" style="left: 260px; top: 117px;" >
        <i class="fa fa-circle-notch" ></i> <span>Rulet</span>
      </div>
    </div>
    `

    // Append menu to wrapper
    wrapper.appendChild(menuContainer)
    wrapper.appendChild(button)

    // Handle menu toggle
    let isOpen = false

    wrapper.addEventListener('click', function (e) {
      e.preventDefault()
      e.stopImmediatePropagation()
      isOpen = !isOpen

      if (isOpen) {
        wrapper.classList.add('active')
        menuContainer.classList.add('active')
      } else {
        closeMenu()
      }
    })

    function closeMenu() {
      isOpen = false
      wrapper.classList.remove('active')
      menuContainer.classList.remove('active')
    }

    // Menu item clicks
    const menuSectors = menuContainer.querySelectorAll('.menu-sector')

    menuSectors.forEach(sector => {
      sector.addEventListener('click', function (e) {
        e.stopPropagation()
        const action = this.getAttribute('data-action')
        console.log('Selected:', action)

        // Handle navigation based on action
        switch (action) {
          case 'sports':
            window.location.href = '/tr/bet/sports'
            break
          case 'slots':
            window.location.href = '/tr/games/slots'
            break
          case 'poker':
            window.location.href = '/tr/games/poker'
            break
          case 'roulette':
            window.location.href = '/tr/games/roulette'
            break
          case 'blackjack':
            window.location.href = '/tr/games/blackjack'
            break
          case 'bonus':
            window.location.href = '/tr/promotions'
            break
        }

        closeMenu()
      })
    })

    // Close menu when clicking outside
    document.addEventListener('click', function (e) {
      if (!wrapper.contains(e.target) && isOpen) {
        closeMenu()
      }
    })

    // Prevent menu from closing when clicking inside
    menuContainer.addEventListener('click', function (e) {
      if (e.target === menuContainer || e.target.classList.contains('menu-svg')) {
        e.stopPropagation()
      }
    })

    // Cleanup function
    window.addEventListener(
      '@makrobet/unload/sidebar',
      () => {
        wrapper.remove()
        kill()
      },
      { once: true }
    )

    return () => {
      wrapper.remove()
    }
  }
})
