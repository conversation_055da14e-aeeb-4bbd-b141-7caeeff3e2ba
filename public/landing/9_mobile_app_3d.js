// 3D Mobile App Section JavaScript
window.$LANDING.onLifecycle({
  selector: `.makrobet-landing`,
  page: /^\/\w+$/,
  onMount: (_, __, kill) => {
    // Configuration
    const IPHONE_MODEL_URL = 'https://cdn.makroz.org/makrobet/models/iphone-15/iphone-new.obj'
    const IPHONE_MTL_URL = 'https://cdn.makroz.org/makrobet/models/iphone-15/iphone-new.mtl'

    // Three.js variables
    let scene, camera, renderer, phoneGroup
    let isHovering = false
    let animationId

    // Function to detect WebGL support
    function detectWebGLSupport() {
      try {
        const canvas = document.createElement('canvas')
        const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl')
        return !!gl
      } catch (e) {
        return false
      }
    }

    async function loadScript(src) {
      const script = document.createElement('script')
      script.src = src

      return new Promise((resolve, reject) => {
        script.onload = resolve
        script.onerror = reject

        document.head.appendChild(script)
      })
    }

    // Function to load Three.js modules using ES6 imports
    async function loadThreeJSModules() {
      try {
        await loadScript('https://cdn.jsdelivr.net/npm/three@0.147.0/build/three.min.js')
        await Promise.all([
          loadScript('https://cdn.jsdelivr.net/npm/three@0.147.0/examples/js/loaders/OBJLoader.js'),
          loadScript('https://cdn.jsdelivr.net/npm/three@0.147.0/examples/js/loaders/MTLLoader.js')
        ])

        console.log('Three.js modules loaded successfully')
        return true
      } catch (error) {
        console.error('Failed to load Three.js modules:', error)
        throw error
      }
    }

    // Function to load iPhone OBJ model with MTL materials
    function loadiPhoneModel() {
      return new Promise((resolve, reject) => {
        // Create loaders
        const mtlLoader = window.THREE.MTLLoader ? new window.THREE.MTLLoader() : new THREE.MTLLoader()
        const objLoader = window.THREE.OBJLoader ? new window.THREE.OBJLoader() : new THREE.OBJLoader()

        console.log('Starting iPhone OBJ and MTL loading...')

        // First, load MTL materials
        mtlLoader.load(
          IPHONE_MTL_URL,
          materials => {
            console.log('MTL materials loaded successfully')
            materials.preload()

            // Set the materials for the OBJ loader
            objLoader.setMaterials(materials)

            // Load the OBJ model
            objLoader.load(
              IPHONE_MODEL_URL,
              object => {
                console.log('iPhone OBJ model loaded successfully')

                // Apply additional settings to all meshes
                object.traverse(child => {
                  if (child.isMesh) {
                    // Enable shadows
                    child.castShadow = true
                    child.receiveShadow = true

                    // Ensure material properties (remove backface)
                    if (child.material) {
                      child.material.side = THREE.FrontSide
                    }
                  }
                })

                // Scale and position the model appropriately (reduced size)
                object.scale.setScalar(0.5)
                object.rotation.x = 0
                object.rotation.y = 0
                object.rotation.z = 0

                resolve(object)
              },
              progress => {
                console.log('OBJ Loading progress:', (progress.loaded / progress.total) * 100 + '%')
              },
              error => {
                console.error('Error loading OBJ model:', error)
                reject(error)
              }
            )
          },
          progress => {
            console.log('MTL Loading progress:', (progress.loaded / progress.total) * 100 + '%')
          },
          error => {
            console.error('Error loading MTL materials:', error)

            // Fallback: try to load OBJ without materials
            console.log('Attempting to load OBJ without materials...')
            objLoader.load(
              IPHONE_MODEL_URL,
              object => {
                console.log('iPhone OBJ model loaded without materials')

                // Apply basic material to all meshes
                object.traverse(child => {
                  if (child.isMesh) {
                    child.material = new THREE.MeshLambertMaterial({
                      color: 0x1a1a1a,
                      side: THREE.FrontSide
                    })
                    child.castShadow = true
                    child.receiveShadow = true
                  }
                })

                object.scale.setScalar(0.5)
                resolve(object)
              },
              undefined,
              objError => {
                console.error('Error loading OBJ model without materials:', objError)
                reject(objError)
              }
            )
          }
        )
      })
    }

    // Function to detect if device is mobile
    function isMobileDevice() {
      return (
        window.innerWidth <= 768 ||
        /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
      )
    }

    // Function to get the appropriate container based on device type
    function getPhoneContainer() {
      const isMobile = isMobileDevice()
      const containerId = isMobile ? '#phone-3d-container-mobile' : '#phone-3d-container-desktop'
      return document.querySelector(containerId)
    }

    // Function to initialize 3D scene
    async function init3DScene() {
      const container = getPhoneContainer()
      if (!container) {
        console.warn('Phone container not found for current device type')
        return
      }

      // Clear existing content
      container.innerHTML = ''

      // Scene setup
      scene = new THREE.Scene()

      // Camera setup
      camera = new THREE.PerspectiveCamera(75, container.offsetWidth / container.offsetHeight, 0.1, 1000)
      camera.position.set(0, 3.5, 7.5)
      camera.rotation.set((-0.1 * Math.PI) / 180, (-9.1 * Math.PI) / 180, (-14.99 * Math.PI) / 180)

      // Renderer setup
      renderer = new THREE.WebGLRenderer({
        antialias: true,
        alpha: true,
        powerPreference: 'high-performance'
      })
      renderer.setPixelRatio(2)
      // renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
      renderer.setSize(container.offsetWidth, container.offsetHeight)
      renderer.setClearColor(0x000000, 0)
      renderer.shadowMap.enabled = true
      renderer.shadowMap.type = THREE.PCFSoftShadowMap
      container.appendChild(renderer.domElement)

      // Lighting
      const ambientLight = new THREE.AmbientLight(0xffffff, 1)
      scene.add(ambientLight)

      // const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
      const directionalLight = new THREE.DirectionalLight(0xffffff, 1)
      directionalLight.position.set(0, 0.5, 10)
      directionalLight.castShadow = true
      scene.add(directionalLight)

      // Create phone group
      phoneGroup = new THREE.Group()

      try {
        // Load iPhone OBJ model with MTL materials
        const iphoneModel = await loadiPhoneModel()
        console.log('iPhone OBJ model loaded and added to scene')

        // Add iPhone model to phone group
        phoneGroup.add(iphoneModel)
      } catch (error) {
        console.error('Failed to load iPhone OBJ model, using fallback geometry:', error)

        // Fallback to simple geometry if OBJ loading fails
        const fallbackGeometry = new THREE.BoxGeometry(1, 2, 0.1)
        const fallbackMaterial = new THREE.MeshLambertMaterial({
          color: 0x1a1a1a
        })
        const fallbackPhone = new THREE.Mesh(fallbackGeometry, fallbackMaterial)
        fallbackPhone.castShadow = true
        phoneGroup.add(fallbackPhone)
      }

      console.log(phoneGroup)

      // Add phone group to scene
      scene.add(phoneGroup)

      // Set initial presentation rotation (idle state)
      // Slight tilt to show the phone in an attractive presentation angle
      const qX = new THREE.Quaternion()
      const qY = new THREE.Quaternion()
      const qZ = new THREE.Quaternion()

      qX.setFromAxisAngle(new THREE.Vector3(1, 0, 0), (-12.9 * Math.PI) / 180) // 45° X
      qY.setFromAxisAngle(new THREE.Vector3(0, 1, 0), (-43.7 * Math.PI) / 180) // 90° Y
      qZ.setFromAxisAngle(new THREE.Vector3(0, 0, 1), (-14 * Math.PI) / 180) // 30° Z

      phoneGroup.quaternion.copy(qY).multiply(qX).multiply(qZ)

      phoneGroup.scale.x = 1
      phoneGroup.scale.y = 1
      phoneGroup.scale.z = 1

      const cameraQuat = camera.quaternion.clone()
      const offsetQuat = new THREE.Quaternion()

      phoneGroup.userData.idleQuat = phoneGroup.quaternion.clone()
      phoneGroup.userData.flatQuat = cameraQuat.multiply(offsetQuat)

      // Start animation loop
      // renderer.render(scene, camera);
      animate()
    }

    // Animation loop
    function animate() {
      // schedule next frame
      animationId = requestAnimationFrame(animate)

      if (!phoneGroup) return

      // Rotation via quaternion slerp
      const { idleQuat, flatQuat } = phoneGroup.userData
      const targetQuat = isHovering ? flatQuat : idleQuat
      const rotLerp = isHovering ? 0.08 : 0.05
      phoneGroup.quaternion.slerp(targetQuat, rotLerp)

      // Uniform scale lerp
      const tScale = isHovering ? 1.05 : 1.0
      const scaleLerp = isHovering ? 0.1 : 0.05
      phoneGroup.scale.x += (tScale - phoneGroup.scale.x) * scaleLerp
      phoneGroup.scale.y += (tScale - phoneGroup.scale.y) * scaleLerp
      phoneGroup.scale.z += (tScale - phoneGroup.scale.z) * scaleLerp

      // Render
      renderer.render(scene, camera)
    }

    // Function to setup hover effects
    function setupHoverEffects() {
      const container = getPhoneContainer()
      if (!container) return

      container.addEventListener('mouseenter', () => {
        isHovering = true
        container.style.cursor = 'pointer'
      })

      container.addEventListener('mouseleave', () => {
        isHovering = false
        container.style.cursor = 'default'
      })

      // Touch events for mobile
      container.addEventListener('touchstart', () => {
        isHovering = true
      })

      container.addEventListener('touchend', () => {
        setTimeout(() => {
          isHovering = false
        }, 2000)
      })
    }

    // Function to handle window resize
    function handleResize() {
      const container = getPhoneContainer()
      if (!container || !camera || !renderer) return

      camera.aspect = container.offsetWidth / container.offsetHeight
      camera.updateProjectionMatrix()
      renderer.setSize(container.offsetWidth, container.offsetHeight)
    }

    // Function to handle device type change (orientation change, window resize)
    let currentDeviceType = isMobileDevice()
    function handleDeviceTypeChange() {
      const newDeviceType = isMobileDevice()
      if (newDeviceType !== currentDeviceType) {
        currentDeviceType = newDeviceType
        console.log('Device type changed, reinitializing 3D scene')

        // Cleanup current scene
        if (animationId) {
          cancelAnimationFrame(animationId)
        }
        if (renderer) {
          renderer.dispose()
        }

        // Reinitialize with new container
        setTimeout(() => {
          initialize()
        }, 100)
      } else {
        handleResize()
      }
    }

    // Initialize everything
    async function initialize() {
      console.log('3D Mobile App section initialized')

      // Check WebGL support first
      if (!detectWebGLSupport()) {
        console.warn('WebGL not supported, falling back to original implementation')
        document.body.classList.add('no-webgl')
        return
      }

      try {
        // Load Three.js library using import maps
        // await setupThreeJSImportMap();
        await loadThreeJSModules()
        console.log('Three.js loaded successfully')

        // Initialize 3D scene
        await init3DScene()
        console.log('3D scene initialized')

        // Remove loading state
        const container = getPhoneContainer()
        if (container) {
          container.classList.remove('loading')
          container.classList.add('interactive')
        }

        // Setup interactions
        setupHoverEffects()

        // Handle window resize and device type changes
        window.addEventListener('resize', handleDeviceTypeChange)
      } catch (error) {
        console.error('Failed to initialize 3D mobile app section:', error)

        // Mark container as error state
        const container = getPhoneContainer()
        if (container) {
          container.classList.remove('loading')
          container.classList.add('error')
        }

        // Fallback to original implementation
        console.log('Falling back to original mobile app implementation')
      }
    }

    // Cleanup function
    function cleanup() {
      if (animationId) {
        cancelAnimationFrame(animationId)
      }

      if (renderer) {
        renderer.dispose()
      }

      window.removeEventListener('resize', handleDeviceTypeChange)
      console.log('3D Mobile App section cleaned up')
    }

    // Start initialization
    initialize()

    // Return cleanup function
    kill(() => cleanup())
  }
})
