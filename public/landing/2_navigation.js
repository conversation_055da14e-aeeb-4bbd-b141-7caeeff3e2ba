// Navigation Image Hover Effects with Dual Image Rendering
window.$LANDING.onLifecycle({
  selector: `.makrobet-landing`,
  page: /^\/\w+$/,
  onMount: (_, __, kill) => {
    // Configuration
    const PASSIVE_SUFFIX = `pasif.png`
    const ACTIVE_SUFFIX = `aktif.png`

    // Get all navigation images
    const navImages = document.querySelectorAll(`.nav-image`)

    function createDualImageStructure() {
      navImages.forEach(img => {
        const passiveSrc = img.src
        const activeSrc = passiveSrc.replace(PASSIVE_SUFFIX, ACTIVE_SUFFIX)

        const navigatePath = img.getAttribute('data-navigate')
        const navigatePathM = img.getAttribute('data-navigate-m')

        // Create container for both images
        const container = document.createElement('div')
        container.className = 'nav-image-container'
        container.style.cssText = `
        position: relative;
        display: inline-block;
        cursor: pointer;
      `
        // Handle navigation for the container
        let isScrolling = false
        let touchStartY = 0

        const handleNavigation = e => {
          e.preventDefault()

          if (window.location.origin.includes('m.')) {
            if (navigatePathM) {
              $LANDING.navigate(navigatePathM)
            } else if (navigatePath) {
              $LANDING.navigate(navigatePath)
            }
          } else {
            if (navigatePath) {
              $LANDING.navigate(navigatePath)
            }
          }
        }

        if (img.src.includes('poker') === false) {
          container.addEventListener('click', handleNavigation)

          // Add touch support with scroll detection
          container.addEventListener('touchstart', e => {
            isScrolling = false
            touchStartY = e.touches[0].clientY
          })

          container.addEventListener('touchmove', e => {
            const touchMoveY = e.touches[0].clientY
            const deltaY = Math.abs(touchMoveY - touchStartY)

            // If moved more than 10px vertically, consider it scrolling
            if (deltaY > 10) {
              isScrolling = true
            }
          })

          container.addEventListener('touchend', e => {
            // Don't handle touch if user was scrolling
            if (isScrolling) {
              return
            }

            e.preventDefault()
            handleNavigation(e)
          })
        }

        // Create passive image (base layer, always visible)
        const passiveImg = document.createElement('img')
        passiveImg.src = passiveSrc
        passiveImg.className = 'nav-image-passive'
        passiveImg.alt = img.alt
        passiveImg.style.cssText = `
        width: 100%;
        height: 100%;
      `

        // Create active image (overlay layer, shown on hover)
        const activeImg = document.createElement('img')
        activeImg.src = activeSrc
        activeImg.className = 'nav-image-active'
        activeImg.alt = img.alt
        activeImg.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0;
        transition: opacity 0.2s ease;
        pointer-events: none;
      `

        // Copy attributes from original image
        if (img.getAttribute('tabindex')) {
          container.setAttribute('tabindex', img.getAttribute('tabindex'))
        }
        if (img.getAttribute('role')) {
          container.setAttribute('role', img.getAttribute('role'))
        }

        // Add both images to container
        container.appendChild(passiveImg)
        container.appendChild(activeImg)

        // Replace original image with container
        img.parentNode.replaceChild(container, img)

        console.log(`Dual image structure created for: ${passiveSrc}`)
      })
    }

    function setupHoverEffects() {
      const navContainers = document.querySelectorAll(`.nav-image-container`)

      navContainers.forEach(container => {
        const passiveImg = container.querySelector('.nav-image-passive')
        const activeImg = container.querySelector('.nav-image-active')

        if (!passiveImg || !activeImg) {
          console.warn(`Passive or active image not found in container`)
          return
        }

        // Mouse enter - show active image on top
        container.addEventListener(`mouseenter`, () => {
          activeImg.style.opacity = '1'
        })

        // Mouse leave - hide active image
        container.addEventListener(`mouseleave`, () => {
          activeImg.style.opacity = '0'
        })

        // Touch start for mobile devices
        container.addEventListener(`touchstart`, () => {
          activeImg.style.opacity = '1'
        })

        // Touch end for mobile devices
        container.addEventListener(`touchend`, () => {
          setTimeout(() => {
            activeImg.style.opacity = '0'
          }, 150) // Small delay to show the active state
        })

        // Add keyboard navigation support
        container.addEventListener(`keydown`, function (e) {
          if (e.key === `Enter` || e.key === ` `) {
            e.preventDefault()
            this.click()
          }
        })

        // Make container focusable for accessibility
        container.setAttribute(`tabindex`, `0`)
        container.setAttribute(`role`, `button`)
      })
    }

    // Initialize
    function initialize() {
      if (navImages.length === 0) {
        console.warn(`Navigasyon görseli bulunamadı`)
        return
      }

      console.log(`${navImages.length} navigasyon görseli bulundu`)

      // Create dual image structure first
      createDualImageStructure()

      // Set up hover effects
      setupHoverEffects()

      console.log(`Navigasyon hover efektleri başlatıldı`)
    }

    // Start initialization
    initialize()

    window.addEventListener(
      '@makrobet/unload/landing',
      () => {
        kill()
      },
      { once: true }
    )
  }
})
