/* Promotions Section */

.promotions-section {
  position: relative;
  padding: 0 0 20px 0;
}

.promotions-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
  position: relative;
  z-index: 1;
}

.promotions-header {
  display: none;
}

.promotions-content {
  position: relative;
}

.promotions-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 8px;
  width: 100%;
}

/* First row: 3 items, each taking 2 columns (1/3 width) */
.promotion-card:nth-child(1) {
  grid-column: span 2;
}

.promotion-card:nth-child(2) {
  grid-column: span 2;
}

.promotion-card:nth-child(3) {
  grid-column: span 2;
}

/* Second row: 2 items, each taking 3 columns (1/2 width) */
.promotion-card:nth-child(4) {
  grid-column: span 3;
}

.promotion-card:nth-child(5) {
  grid-column: span 3;
}

/* Promotion Cards */
.promotion-card {
  background: linear-gradient(135deg, rgb(var(--secondary-color)) 0%, #1e1e3f 100%);
  border-radius: 16px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(var(--primary-color), 0.1);
  position: relative;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.2),
    0 2px 8px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  height: 200px;
}

.promotion-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg,
      rgba(var(--primary-color), 0.05) 0%,
      transparent 30%,
      rgba(var(--primary-color), 0.03) 70%,
      transparent 100%);
  z-index: 1;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  border-radius: 16px;
}

.promotion-card::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg,
      rgba(var(--primary-color), 0.3) 0%,
      transparent 25%,
      rgba(var(--primary-color), 0.2) 50%,
      transparent 75%,
      rgba(var(--primary-color), 0.3) 100%);
  z-index: -1;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  border-radius: 18px;
}

.promotion-card:hover::before {
  opacity: 1;
}

.promotion-card:hover::after {
  opacity: 1;
}

.promotion-card:hover {
  transform: translateY(-4px) scale(1.02);
  border-color: rgba(var(--primary-color), 0.4);
}

/* Promotion Image */
.promotion-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: top left;
  display: block;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}

/* Mobile optimization for promotion images */
@media (max-width: 768px) {
  .promotion-image {
    object-position: bottom left;
  }
}

.promotion-card:hover .promotion-image {
  transform: scale(1.05);
}

/* Promotion Content */
.promotion-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 28px 20px 14px 20px;
  background: linear-gradient(to top,
      rgba(0, 0, 0, 0.95) 0%,
      rgba(0, 0, 0, 0.85) 40%,
      rgba(0, 0, 0, 0.6) 70%,
      rgba(0, 0, 0, 0.3) 85%,
      transparent 100%);
  z-index: 3;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.promotion-title {
  font-size: 1.1rem;
  font-weight: 700;
  color: rgb(var(--primary-color));
  line-height: 1.3;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.8);
}

.promotion-description {
  font-size: 0.75rem;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.8);
}

.promotion-card:hover .promotion-title {
  color: #ffd700;
  text-shadow: 0 2px 8px rgba(var(--primary-color), 0.3);
}

.promotion-card:hover .promotion-description {
  color: rgba(255, 255, 255, 0.95);
}

/* Loading Placeholders */
.promotion-card.loading-placeholder {
  animation: pulse 1.5s ease-in-out infinite;
  background: rgba(var(--secondary-color), 0.4);
}

.promotion-image-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.1) 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  position: absolute;
  top: 0;
  left: 0;
}

.promotion-content-placeholder {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 28px 20px 8px 20px;
  background: linear-gradient(to top,
      rgba(0, 0, 0, 0.95) 0%,
      rgba(0, 0, 0, 0.85) 40%,
      rgba(0, 0, 0, 0.6) 70%,
      rgba(0, 0, 0, 0.3) 85%,
      transparent 100%);
  display: flex;
  flex-direction: column;
  gap: 10px;
  min-height: 100px;
}

.promotion-title-placeholder {
  height: 20px;
  width: 90%;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.1) 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
}

.promotion-description-placeholder {
  height: calc(1.4em * 2);
  width: 85%;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.1) 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
}

/* Animations */
@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.7;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }

  100% {
    background-position: 200% 0;
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .promotions-container {
    padding: 0 20px;
  }

  .promotions-grid {
    gap: 20px;
  }

  /* Maintain the same grid layout on tablets */

  .promotion-card {
    height: 180px;
  }

  .promotion-content {
    padding: 24px 18px;
    min-height: 90px;
  }

  .promotion-content-placeholder {
    padding: 24px 18px;
    min-height: 90px;
  }

  .promotion-title {
    font-size: 1rem;
  }

  .promotion-description {
    font-size: 0.85rem;
  }
}

/* Promotions Slider Styles for Mobile */
.promotions-section .game-section-header {
  display: none;
}

.promotions-slider {
  display: none;
  position: relative;
  overflow: hidden;
  border-radius: 16px;
}

.promotions-track {
  display: flex;
  gap: 16px;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.promotions-track .promotion-card {
  flex: 0 0 calc(100vw - 32px);
  height: 160px;
}

@media (max-width: 768px) {
  .promotions-section {
    display: none;
  }

  .promotions-container {
    padding: 0 16px;
  }

  /* Hide grid on mobile */
  .promotions-grid {
    display: none;
  }

  /* Show slider on mobile */
  .promotions-section .game-section-header {
    display: flex;
  }

  .promotions-slider {
    display: block;
  }

  .promotions-track .promotion-card {
    flex: 0 0 calc(100vw - 32px);
    height: 140px;
  }

  .promotion-content {
    padding: 20px 16px;
    min-height: 80px;
  }

  .promotion-content-placeholder {
    padding: 20px 16px;
    min-height: 80px;
  }

  .promotion-title {
    font-size: 0.95rem;
  }

  .promotion-description {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .promotions-track .promotion-card {
    flex: 0 0 calc(100vw - 32px);
    height: 120px;
  }

  .promotion-content {
    padding: 18px 14px;
    min-height: 70px;
  }

  .promotion-content-placeholder {
    padding: 18px 14px;
    min-height: 70px;
  }

  .promotion-title {
    font-size: 0.9rem;
  }

  .promotion-description {
    font-size: 0.75rem;
  }
}
