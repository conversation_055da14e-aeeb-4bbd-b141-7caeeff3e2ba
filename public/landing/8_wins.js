// Wins Section JavaScript
window.$LANDING.onLifecycle({
  selector: `.makrobet-landing`,
  page: /^\/\w+$/,
  onMount: (_, __, kill) => {
    // Configuration
    const CONSUMER_API_URL = `https://pn17.pfnow100.com/api/tr/consumer`
    const TOP_WINS_API_URL = `${window.origin}/odin/api/generic/getEgtJackpotData/${window.host}`
    const CONSUMER_POLL_INTERVAL = 10 * 5000 // 5 seconds
    const TOP_WINS_POLL_INTERVAL = 5 * 60 * 1000 // 5 minutes

    // State
    let topWinners = []
    let consumerPollInterval = null
    let topWinsPollInterval = null

    // DOM elements
    const topWinsTrack = document.getElementById(`top-wins-track`)
    const dailyWinsTbody = document.getElementById(`daily-wins-tbody`)
    const weeklyWinsTbody = document.getElementById(`weekly-wins-tbody`)
    const monthlyWinsTbody = document.getElementById(`monthly-wins-tbody`)

    // Check if elements exist
    if (!topWinsTrack || !dailyWinsTbody || !weeklyWinsTbody || !monthlyWinsTbody) {
      console.error(`Wins section: Required DOM elements not found`)
      return
    }

    // Format currency amount
    function formatAmount(amount) {
      const numAmount = parseFloat(amount)
      if (numAmount >= 1000000000) {
        return `${(numAmount / 1000000000).toFixed(1)}B`
      } else if (numAmount >= 1000000) {
        return `${(numAmount / 1000000).toFixed(1)}M`
      } else if (numAmount >= 1000) {
        return `${(numAmount / 1000).toFixed(1)}K`
      }
      return numAmount.toLocaleString()
    }

    // Format date for top wins
    function formatTopWinDate(dateString) {
      const date = new Date(dateString)
      const now = new Date()
      const diffMs = now - date
      const diffHours = Math.floor(diffMs / 3600000)
      const diffDays = Math.floor(diffMs / 86400000)

      if (diffHours < 1) {
        return `just now`
      } else if (diffHours < 24) {
        return `${diffHours}h ago`
      } else if (diffDays < 7) {
        return `${diffDays}d ago`
      } else {
        return date.toLocaleDateString(`en-US`, { month: `short`, day: `numeric` })
      }
    }

    // Create game cell content (without wrapper div)
    function createGameCellContent(gameId, gameName) {
      const imageUrl = `//v3.pro1staticserv.com/common/assets/images/casino/300x200/${gameId}.jpg`
      const fallbackImageUrl = `data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iMzAiIHZpZXdCb3g9IjAgMCA0MCAzMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjMwIiBmaWxsPSIjMmIyYjRmIi8+Cjx0ZXh0IHg9IjIwIiB5PSIxNSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZmlsbD0iI2ZiZDEyZCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjgiPkdhbWU8L3RleHQ+Cjwvc3ZnPg==`

      const gameImage = document.createElement(`img`)
      gameImage.src = imageUrl
      gameImage.alt = gameName
      gameImage.className = `game-image`
      gameImage.addEventListener(`error`, function () {
        this.src = fallbackImageUrl
      })

      const gameNameSpan = document.createElement(`span`)
      gameNameSpan.className = `game-name`
      gameNameSpan.textContent = gameName
      gameNameSpan.title = gameName // Tooltip for full name

      return { gameImage, gameNameSpan }
    }

    // Create table row for winners
    function createWinnerRow(winner) {
      const row = document.createElement(`tr`)

      // Game cell with proper structure
      const gameCell = document.createElement(`td`)
      gameCell.className = `game-cell`

      // Create game info container
      const gameInfoDiv = document.createElement(`div`)
      gameInfoDiv.className = `game-info`

      const { gameImage, gameNameSpan } = createGameCellContent(winner.csn_game_id, winner.game_name)
      gameInfoDiv.appendChild(gameImage)
      gameInfoDiv.appendChild(gameNameSpan)

      gameCell.appendChild(gameInfoDiv)

      const playerCell = document.createElement(`td`)
      playerCell.textContent = winner.cus
      playerCell.className = `player-name`

      const amountCell = document.createElement(`td`)
      amountCell.className = `win-amount`
      amountCell.textContent = `₺${formatAmount(winner.net_win_amount)}`

      row.appendChild(gameCell)
      row.appendChild(playerCell)
      row.appendChild(amountCell)

      return row
    }

    // Create top win item for marquee
    function createTopWinItem(winner) {
      const item = document.createElement(`div`)
      item.className = `top-win-item`
      const formattedDate = formatTopWinDate(winner.winDate)
      item.textContent = `***** ${formattedDate} tarihinde ₺${formatAmount(winner.winAmount)} kazandı`
      return item
    }

    // Update winners table
    function updateWinnersTable(tbody, winners, noDataMessage) {
      tbody.innerHTML = ``

      if (winners.length === 0) {
        const row = document.createElement(`tr`)
        row.className = `no-data-row`

        const cell = document.createElement(`td`)
        cell.setAttribute(`colspan`, `3`)
        cell.textContent = noDataMessage

        row.appendChild(cell)
        tbody.appendChild(row)
        return
      }

      winners.forEach(winner => {
        const row = createWinnerRow(winner)
        tbody.appendChild(row)
      })
    }

    // Update top wins marquee
    function updateTopWinsMarquee(newTopWinners) {
      if (newTopWinners.length === 0) {
        if (topWinsTrack.children.length === 0) {
          const item = document.createElement(`div`)
          item.className = `top-win-item`
          item.textContent = `No top wins available`
          topWinsTrack.appendChild(item)
        }
        return
      }

      // Clear existing items
      topWinsTrack.innerHTML = ``

      // Create items for infinite scroll (duplicate content)
      const createItemSet = () => {
        newTopWinners.forEach(winner => {
          const item = createTopWinItem(winner)
          topWinsTrack.appendChild(item)
        })
      }

      // Add multiple sets for seamless infinite scroll
      createItemSet() // First set
      createItemSet() // Second set for seamless loop
      createItemSet() // Third set for extra buffer
    }

    // Fetch consumer data (daily, weekly, monthly winners)
    async function fetchConsumerData() {
      try {
        const response = await fetch(CONSUMER_API_URL)

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const data = await response.json()

        if (data.casino_winners) {
          const { daily_winners, weekly_winners, monthly_winners } = data.casino_winners

          // Update mobile tab tables
          updateWinnersTable(dailyWinsTbody, daily_winners || [], `Günlük kazanan bulunmuyor`)
          updateWinnersTable(weeklyWinsTbody, weekly_winners || [], `Haftalık kazanan bulunmuyor`)
          updateWinnersTable(monthlyWinsTbody, monthly_winners || [], `Aylık kazanan bulunmuyor`)

          // Update desktop tables if present
          const dailyDesktop = document.getElementById('daily-wins-tbody-desktop')
          const weeklyDesktop = document.getElementById('weekly-wins-tbody-desktop')
          const monthlyDesktop = document.getElementById('monthly-wins-tbody-desktop')
          if (dailyDesktop) updateWinnersTable(dailyDesktop, daily_winners || [], `Günlük kazanan bulunmuyor`)
          if (weeklyDesktop) updateWinnersTable(weeklyDesktop, weekly_winners || [], `Haftalık kazanan bulunmuyor`)
          if (monthlyDesktop) updateWinnersTable(monthlyDesktop, monthly_winners || [], `Aylık kazanan bulunmuyor`)
        }
      } catch (error) {
        console.error(`Error fetching consumer data:`, error)
        showConsumerErrorState()
      }
    }

    // Fetch top wins data for marquee
    async function fetchTopWinsData() {
      try {
        const timestamp = Date.now()
        const response = await fetch(`${TOP_WINS_API_URL}?_=${timestamp}`)

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const data = await response.json()

        if (data.topWinners && Array.isArray(data.topWinners)) {
          topWinners = data.topWinners
          updateTopWinsMarquee(topWinners)
        }
      } catch (error) {
        console.error(`Error fetching top wins data:`, error)
        showTopWinsErrorState()
      }
    }

    // Show error state for consumer data
    function showConsumerErrorState() {
      const errorMessage = `Kazanç verileri yüklenemedi`

      // Show error for all three tables
      ;[dailyWinsTbody, weeklyWinsTbody, monthlyWinsTbody].forEach(tbody => {
        tbody.innerHTML = ``
        const errorRow = document.createElement(`tr`)
        errorRow.className = `error-row`

        const errorCell = document.createElement(`td`)
        errorCell.setAttribute(`colspan`, `3`)
        errorCell.textContent = errorMessage

        errorRow.appendChild(errorCell)
        tbody.appendChild(errorRow)
      })
    }

    // Show error state for top wins marquee
    function showTopWinsErrorState() {
      topWinsTrack.innerHTML = ``
      const errorItem = document.createElement(`div`)
      errorItem.className = `top-win-item`
      errorItem.textContent = `En büyük kazançlar yüklenemedi`
      topWinsTrack.appendChild(errorItem)
    }

    // Start polling
    function startPolling() {
      // Initial load
      fetchConsumerData()
      fetchTopWinsData()

      // Set up separate intervals
      consumerPollInterval = setInterval(fetchConsumerData, CONSUMER_POLL_INTERVAL)
      topWinsPollInterval = setInterval(fetchTopWinsData, TOP_WINS_POLL_INTERVAL)
    }

    // Stop polling
    function stopPolling() {
      if (consumerPollInterval) {
        clearInterval(consumerPollInterval)
        consumerPollInterval = null
      }
      if (topWinsPollInterval) {
        clearInterval(topWinsPollInterval)
        topWinsPollInterval = null
      }
    }

    // Initialize
    function initialize() {
      startPolling()
    }

    // Cleanup on page unload
    window.addEventListener(`beforeunload`, stopPolling)

    // Start the wins section
    initialize()

    // --- Responsive winners section: tabs for mobile, columns for desktop/tablet ---
    function setupWinsTabs() {
      const isMobile = window.innerWidth <= 768
      const tabsContainer = document.querySelector('.wins-tabs-container')
      const winsContent = document.querySelector('.wins-content')
      // If on mobile, show tabs and hide columns
      if (isMobile) {
        if (tabsContainer) tabsContainer.style.display = ''
        if (winsContent) winsContent.style.display = 'none'
        // Tab logic
        const btns = Array.from(document.querySelectorAll('.wins-tab-btn'))
        const panels = Array.from(document.querySelectorAll('.wins-tab-panel'))
        btns.forEach(btn => {
          btn.addEventListener('click', function () {
            btns.forEach(b => b.classList.remove('active'))
            btn.classList.add('active')
            const tab = btn.getAttribute('data-tab')
            panels.forEach(panel => {
              if (panel.getAttribute('data-tab') === tab) {
                panel.style.display = 'block'
              } else {
                panel.style.display = 'none'
              }
            })
          })
        })
        // Show only the active panel on load
        const activeBtn = btns.find(b => b.classList.contains('active')) || btns[0]
        if (activeBtn) {
          const tab = activeBtn.getAttribute('data-tab')
          panels.forEach(panel => {
            if (panel.getAttribute('data-tab') === tab) {
              panel.style.display = 'block'
            } else {
              panel.style.display = 'none'
            }
          })
        }
      } else {
        // On desktop/tablet, show columns and hide tabs
        if (tabsContainer) tabsContainer.style.display = 'none'
        if (winsContent) winsContent.style.display = ''
      }
    }
    // Call after DOM is ready
    setTimeout(setupWinsTabs, 0)
    // Also update on resize
    window.addEventListener('resize', setupWinsTabs)

    window.addEventListener(
      '@makrobet/unload/landing',
      () => {
        kill()
      },
      { once: true }
    )
  }
})
