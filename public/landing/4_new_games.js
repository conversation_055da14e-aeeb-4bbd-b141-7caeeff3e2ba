// New Games Section JavaScript
window.$LANDING.onLifecycle({
  selector: `.makrobet-landing`,
  page: /^\/\w+$/,
  onMount: (_, __, kill) => {
    // Configuration
    const GAMES_API = `${window.origin}/odin/api/user/casinoapi/getReservedGames`
    const SLIDER_API = `https://pn17.pfnow100.com/api/tr/consumer`
    const SLIDER_TYPE = 39 // New games slider type
    const SLIDER_DEVICE_TYPE = window.origin.includes('//m.') ? 2 : 1
    const IMAGE_BASE_URL = window.origin

    // State
    let games = []
    let gameSlides = []

    // Function to parse custom_class string
    function parseCustomClass(customClass) {
      if (!customClass) return {}

      const result = {}
      const pairs = customClass.split(';')

      pairs.forEach(pair => {
        const [key, value] = pair.split('=')
        if (key && value) {
          result[key.trim()] = value.trim()
        }
      })

      return result
    }

    // Function to fetch game slides
    async function fetchGameSlides() {
      try {
        console.log(`API'den yeni oyun slaytları alınıyor...`)

        const response = await fetch(SLIDER_API, {
          method: `GET`,
          headers: {
            [`Content-Type`]: `application/json`
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const data = await response.json()

        if (!data.sliders || !Array.isArray(data.sliders)) {
          throw new Error(`Invalid response format`)
        }

        return data.sliders.filter(s => s.type === SLIDER_TYPE && s.m_t === SLIDER_DEVICE_TYPE)
      } catch (error) {
        console.error(`Yeni oyun slaytları alınırken hata:`, error)
        return []
      }
    }

    // Function to fetch games (both slots and live casino)
    async function fetchGames() {
      try {
        console.log(`API'den oyunlar alınıyor (slots ve live casino)...`)

        // Fetch both slots and live casino games in parallel
        const [slotsResponse, liveCasinoResponse] = await Promise.all([
          fetch(GAMES_API, {
            method: `POST`,
            headers: {
              [`Content-Type`]: `application/json`
            },
            body: JSON.stringify({
              requestBody: {
                currencyId: 1,
                gameType: `casino`
              },
              languageId: 1,
              device: `d`
            })
          }),
          fetch(GAMES_API, {
            method: `POST`,
            headers: {
              [`Content-Type`]: `application/json`
            },
            body: JSON.stringify({
              requestBody: {
                currencyId: 1,
                gameType: `liveCasino`
              },
              languageId: 1,
              device: `d`
            })
          })
        ])

        if (!slotsResponse.ok || !liveCasinoResponse.ok) {
          throw new Error(`HTTP error! slots: ${slotsResponse.status}, live: ${liveCasinoResponse.status}`)
        }

        const [slotsData, liveCasinoData] = await Promise.all([slotsResponse.json(), liveCasinoResponse.json()])

        // Combine both game types
        const slotsGames = Array.isArray(slotsData.data?.games)
          ? slotsData.data.games.map(g => ({ ...g, isLive: false }))
          : []
        const liveGames = Array.isArray(liveCasinoData.data?.games)
          ? liveCasinoData.data.games.map(g => ({ ...g, isLive: true }))
          : []
        const allGames = [...slotsGames, ...liveGames]

        console.log(`${slotsGames.length} slot oyunu ve ${liveGames.length} canlı casino oyunu yüklendi`)
        return allGames
      } catch (error) {
        console.error(`Oyunlar alınırken hata:`, error)
        return []
      }
    }

    // Function to load and display new games
    async function loadNewGames() {
      try {
        // Load games and slides in parallel
        const [gamesData, slidesData] = await Promise.all([fetchGames(), fetchGameSlides()])

        games = gamesData
        gameSlides = slidesData

        console.log(`${gameSlides.length} yeni oyun slaytı yüklendi`)

        // Map slides to games like tournaments
        const enhancedGames = gameSlides
          .map(slide => {
            const customData = parseCustomClass(slide.custom_class)

            const gameData = games.find(g => g.id.toString() === customData.id)
            if (!gameData) return null

            return {
              ...gameData,
              image: slide.path.startsWith('http') ? slide.path : `${IMAGE_BASE_URL}${slide.path}`
            }
          })
          .filter(Boolean)

        if (enhancedGames.length === 0) {
          enhancedGames.push(
            ...games
              .filter(g => g.newGame)
              .slice(0, 20)
              .map(e => ({
                ...e,
                image: `/cdn/common/assets/images/casino/300x200/${e.id}.jpg`
              }))
          )
        }

        // Function to create game card with enhanced image
        function createGameCard(game) {
          return window.GameSliderUtils.createGameCard(game, game.image)
        }

        // Create slider with enhanced games
        const newGamesSlider = window.GameSlider({
          sectionId: `new-games`,
          trackId: `new-games-track`,
          prevArrowId: `new-prev-arrow`,
          nextArrowId: `new-next-arrow`,
          games: enhancedGames,
          createCardFunction: createGameCard,
          navigationPath: `/games/casino/detail/normal/:id`,
          noItemsMessage: `No new games found at the moment.`
        })

        newGamesSlider.renderGames()

        console.log(`${enhancedGames.length} yeni oyun yüklendi (slayt ile geliştirilmiş)`)
      } catch (error) {
        console.error(`Yeni oyunlar yüklenirken hata:`, error)
      }
    }

    // Initialize
    loadNewGames()

    window.addEventListener(
      '@makrobet/unload/landing',
      () => {
        kill()
      },
      { once: true }
    )
  }
})
