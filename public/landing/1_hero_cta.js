// Last played slot functions
async function fetchLastPlayedSlot() {
  try {
    // Get current timestamp and calculate 24 hours ago
    const now = Date.now()
    const twentyFourHoursAgo = now - 24 * 60 * 60 * 1000

    const response = await fetch(`${window.location.origin}/odin/api/user/casinoapi/playerGameTransactions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        s7oryo9stv: window.localStorage.getItem('s7oryO9STV').replace(/^"|"$/g, '')
      },
      body: JSON.stringify({
        requestBody: {
          startDate: twentyFourHoursAgo,
          endDate: now
        },
        device: 'd',
        languageId: 2
      }),
      credentials: 'include'
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await response.json()

    if (data.success && data.data && data.data.gameTransactions && data.data.gameTransactions.length > 0) {
      // Get the most recent game transaction
      const lastGame = data.data.gameTransactions[0]
      console.log('Last played game:', lastGame)
      return {
        id: lastGame.gameId,
        name: lastGame.gameName,
        vendorName: lastGame.vendorName
      }
    } else {
      console.log('No recent game transactions found')
      return null
    }
  } catch (error) {
    console.error('Error fetching last played slot:', error)
    return null
  }
}

async function fetchProviders() {
  try {
    const response = await fetch(`${window.origin}/odin/api/user/casinoapi/getReservedVendors/ordered`, {
      method: `POST`,
      headers: {
        [`Content-Type`]: `application/json`
      },
      body: JSON.stringify({
        requestBody: {
          currencyId: 1
        },
        languageId: 1,
        device: `d`
      })
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await response.json()

    if (data.success && data.data && data.data.vendors) {
      return data.data.vendors
    } else {
      console.log('No vendors found')
      return null
    }
  } catch (error) {
    console.error('Error fetching vendors:', error)
    return null
  }
}

async function fetchSlots(providers) {
  try {
    const response = await fetch(`${window.origin}/odin/api/user/casinoapi/getReservedGames`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        requestBody: {
          currencyId: 1,
          gameType: `casino`
        },
        languageId: 1,
        device: `d`
      }),
      credentials: 'include'
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await response.json()

    if (data.success && data.data && data.data.games && data.data.games.length > 0) {
      return data.data.games.map(g => {
        const provider = providers.find(p => p.id === g.vendorId)
        return {
          ...g,
          vendorName: provider ? provider.name : ''
        }
      })
    } else {
      console.log('No slot games found')
      return null
    }
  } catch (error) {
    console.error('Error fetching random slot game:', error)
    return null
  }
}

async function fetchPnCustomer() {
  const response = await fetch('https://pn17.pfnow100.com/api/fpp/customer', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      cid: window.localStorage.getItem('customerCode'),
      pft: window.localStorage.getItem('cGFuZWw'),
      token: JSON.parse(window.localStorage.getItem('s7oryO9STV'))
    })
  })

  if (response.ok) {
    const data = await response.json()
    console.log('User data from pn17.pfnow100.com:', data)
    return data
  } else {
    console.log('First endpoint failed, trying second endpoint...')
  }
}

async function fetchOdinCustomer() {
  // Try the second endpoint using window.location.origin
  const response = await fetch(`${window.location.origin}/odin/api/user/getCurrentCustomer`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      s7oryo9stv: window.localStorage.getItem('s7oryO9STV').replace(/^"|"$/g, '')
    },
    credentials: 'include' // Include cookies for authentication
  })

  if (response.ok) {
    const data = (await response.json())['data']
    console.log('User data from getCurrentCustomer:', data)
    return data
  } else {
    console.error('Second endpoint also failed:', response.status, response.statusText)
  }
}

async function fetchCustomerBonuses() {
  try {
    const response = await fetch(`${window.origin}/odin/api/user/casinoapi/getPlayerBonuses`, {
      method: 'POST',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        s7oryo9stv: window.localStorage.getItem('s7oryO9STV').replace(/^"|"$/g, ''),
        'X-Pgdevice': 'd',
        'X-Pgtradername': '15',
        'X-Pgusername': window.localStorage.getItem('customerCode')
      },
      body: JSON.stringify({ device: 'd', languageId: 1 })
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await response.json()

    if (data.success && data.data && data.data.playerBonuses) {
      return data.data.playerBonuses
    } else {
      console.log('No bonuses found')
      return []
    }
  } catch (error) {
    console.error('Error fetching customer bonuses:', error)
    return []
  }
}

function isLoggedIn() {
  return window.localStorage.getItem('LoggedIn') === 'true'
}

async function fetchCustomer() {
  if (!isLoggedIn()) {
    return null
  }

  const [pn, odin] = await Promise.all([fetchPnCustomer(), fetchOdinCustomer()])

  return {
    pn,
    odin
  }
}

function getGameImageUrl(gameId) {
  if (!gameId) {
    return `${window.location.origin}/cdn/common/assets/images/casino/default.jpg`
  }
  return `${window.location.origin}/cdn/common/assets/images/casino/300x200/${gameId}.jpg`
}

function getRankIcon(rankName) {
  const rankIcons = {
    BRONZE: '🥉',
    SILVER: '🥈',
    GOLD: '🥇',
    PLATINUM: '💎',
    DIAMOND: '💠',
    ELITE: '⭐',
    'MACRO BLACK': '🖤'
  }
  return rankIcons[rankName] || '🥉'
}

function getRankColor(rankName) {
  const rankColors = {
    BRONZE: '#CD7F32',
    SILVER: '#C0C0C0',
    GOLD: '#FFD700',
    PLATINUM: '#E5E4E2',
    DIAMOND: '#B9F2FF',
    ELITE: '#FF6B35',
    'MACRO BLACK': '#1A1A1A'
  }
  return rankColors[rankName] || '#CD7F32'
}

window.$LANDING.onLifecycle({
  selector: `.makrobet-landing`,
  page: /^\/\w+$/,
  onMount: (_, __, kill) => {
    async function init() {
      const customer = await fetchCustomer()

      if (customer) {
        updateUserContent(customer, [], null, null)
        const bonuses = await fetchCustomerBonuses()
        const providers = await fetchProviders()
        const slots = await fetchSlots(providers)
        let lastPlayedSlot = await fetchLastPlayedSlot()
        if (!lastPlayedSlot) {
          const randomIndex = Math.floor(Math.random() * slots.length)
          lastPlayedSlot = slots[randomIndex]
        }

        updateUserContent(customer, slots, lastPlayedSlot, bonuses[0])
      } else {
        showDefaultContent()
      }

      return setupCTANavigationHandlers()
    }

    function displayLastPlayedSlot(slot) {
      const lastPlayedSection = document.getElementById('last-played-section')
      const lastPlayedImage = document.getElementById('last-played-image')
      const lastPlayedName = document.getElementById('last-played-name')
      const lastPlayedProvider = document.getElementById('last-played-provider')
      const lastPlayedBtn = document.getElementById('last-played-btn')
      const playAgainText = document.querySelector('.play-again-text')

      if (!lastPlayedSection) {
        console.warn('Last played section not found')
        return
      }

      // If slot is null, display loader indicator
      if (!slot) {
        console.log('Slot is null, displaying loader')

        // Show loading state
        if (lastPlayedImage) {
          lastPlayedImage.style.display = 'none'
        }

        if (lastPlayedName) {
          lastPlayedName.innerHTML = '<div class="loader-indicator"><div class="spinner"></div></div>'
        }

        if (lastPlayedProvider) {
          lastPlayedProvider.textContent = ''
        }

        if (lastPlayedBtn) {
          // lastPlayedBtn.style.display = 'none'
        }

        // Show the section with loader
        lastPlayedSection.style.display = 'flex'
        return
      }

      // Update game information (restore normal state)
      if (lastPlayedImage) {
        lastPlayedImage.style.display = 'block' // Restore image visibility
        const imageUrl = getGameImageUrl(slot.id)
        lastPlayedImage.src = imageUrl
        lastPlayedImage.alt = slot.name

        // Add error handling for image loading
        lastPlayedImage.onerror = function () {
          // Try alternative image paths
          const fallbackUrls = [
            `${window.location.origin}/cdn/common/assets/images/casino/150x150/${slot.id}.jpg`,
            `${window.location.origin}/cdn/common/assets/images/casino/100x100/${slot.id}.jpg`,
            `${window.location.origin}/cdn/common/assets/images/casino/default.jpg`
          ]

          let currentFallback = 0
          const tryNextFallback = () => {
            if (currentFallback < fallbackUrls.length) {
              this.src = fallbackUrls[currentFallback]
              currentFallback++
            } else {
              // If all fallbacks fail, hide the image
              this.style.display = 'none'
            }
          }

          this.onerror = tryNextFallback
          tryNextFallback()
        }
      }

      if (lastPlayedName) {
        lastPlayedName.innerHTML = '' // Clear any loader content
        lastPlayedName.textContent = slot.name
      }

      if (lastPlayedProvider) {
        lastPlayedProvider.textContent = slot.vendorName
      }

      // Restore button visibility
      if (lastPlayedBtn) {
        lastPlayedBtn.style.display = 'flex'
      }

      // Update the text to "Play again" in Turkish for last played games
      if (playAgainText) {
        playAgainText.textContent = 'Tekrar oyna'
      }

      // Add click handler to the entire game block
      const lastPlayedGame = document.getElementById('last-played-game')

      const handleGameClick = () => {
        // Build game URL using the same pattern as popular games
        const navigationPath = `/games/casino/detail/normal/:id`
        const gameUrl = navigationPath.replace(':id', slot.id)

        if (window.$LANDING && window.$LANDING.navigate) {
          window.$LANDING.navigate(gameUrl)
        } else {
          window.location.href = gameUrl
        }
      }

      if (lastPlayedBtn) {
        lastPlayedBtn.setAttribute('data-game-id', slot.id)
        lastPlayedBtn.onclick = handleGameClick
      }

      if (lastPlayedGame) {
        lastPlayedGame.onclick = handleGameClick
      }

      // Show the last played section
      lastPlayedSection.style.display = 'flex'
      console.log('Last played slot displayed successfully')
    }

    function hideLastPlayedSlot() {
      const lastPlayedSection = document.getElementById('last-played-section')
      if (lastPlayedSection) {
        lastPlayedSection.style.display = 'none'
      }
    }

    function updateUserContent(customer, slots, lastPlayedSlot, bonus) {
      // Get DOM elements
      const defaultContent = document.getElementById('cta-content-default')
      const userContent = document.getElementById('cta-content-user')
      const userName = document.getElementById('profile-name')
      const userRankBadge = document.getElementById('profile-rank-badge')

      if (!defaultContent || !userContent) {
        console.warn('CTA content elements not found')
        return
      }

      // Update user name
      if (userName && customer.odin.firstName) {
        userName.textContent = customer.odin.firstName
      }

      // Update rank information
      if (customer.pn.rank_name && userRankBadge) {
        const rankName = customer.pn.rank_name.toUpperCase()
        const rankColor = customer.pn.color_code

        // userRankIcon.textContent = rankIcon
        userRankBadge.textContent = rankName
        userRankBadge.style.borderColor = rankColor
        userRankBadge.style.color = '#000000'
        userRankBadge.style.backgroundColor = rankColor
      }

      // Update new profile header fields
      const profileInitials = document.getElementById('profile-initials')
      const profileName = document.getElementById('profile-name')
      const profileMeta = document.getElementById('profile-meta')
      const profileRankBadge = document.getElementById('profile-rank-badge')

      if (profileInitials) {
        // Always show initials, never rank icons
        if (customer.odin.firstName && customer.odin.surname) {
          profileInitials.textContent = `${customer.odin.firstName[0].toUpperCase()}${customer.odin.surname[0].toUpperCase()}`
        } else if (customer.odin.firstName) {
          profileInitials.textContent = customer.odin.firstName[0].toUpperCase()
        } else {
          profileInitials.textContent = 'U' // Default fallback
        }
      }
      if (profileName && customer.odin.firstName && customer.odin.surname) {
        profileName.textContent = `${customer.odin.firstName} ${customer.odin.surname}`
      }
      if (profileMeta && customer.pn.code && customer.odin.cityName && customer.odin.countryAbbrvt) {
        profileMeta.textContent = `ID: ${customer.pn.code} \u2022 ${customer.odin.cityName}, ${customer.odin.countryAbbrvt}`
      }
      if (profileRankBadge) {
        if (customer.pn.rank_name) {
          profileRankBadge.textContent = customer.pn.rank_name.toUpperCase()
        } else {
          profileRankBadge.textContent = 'Rütbe yok'
        }
      }

      // Update balance section
      const balanceValue = document.getElementById('balance-value')
      const balanceBadge = document.getElementById('balance-badge')
      const balancePoints = document.getElementById('balance-points')
      const balanceMemberSince = document.getElementById('balance-member-since')
      if (balanceValue) {
        const bal = typeof customer.odin.balance === 'number' ? customer.odin.balance : 0
        balanceValue.textContent = `₺${bal.toFixed(2)}`
      }
      if (balanceBadge) {
        if (customer.odin.balance === 0 || customer.odin.balance === '0' || customer.odin.balance === undefined) {
          balanceBadge.textContent = 'BOŞ'
          balanceBadge.style.display = ''
        } else {
          balanceBadge.style.display = 'none'
        }
      }
      // Check if bonus data exists and has turnoverAmount
      const hasBonus = bonus && bonus.turnoverAmount !== null && bonus.turnoverAmount !== undefined

      if (balancePoints) {
        if (hasBonus) {
          // Display bonus name
          const bonusName = bonus.bonusName || bonus.name || 'Bonus'
          balancePoints.textContent = bonusName
        } else {
          // Display points when no bonus
          let pts = typeof customer.pn.points === 'number' ? customer.pn.points : 0
          if (customer.pn.points === undefined) {
            pts = 0
          }
          balancePoints.textContent = `Puan: ${pts.toFixed(2)}`
        }
      }

      if (balanceMemberSince) {
        if (hasBonus) {
          // Display bonus turnover information
          const turnoverAmount = typeof bonus.turnoverAmount === 'number' ? bonus.turnoverAmount : 0
          const totalTurnover = typeof bonus.totalTurnover === 'number' ? bonus.totalTurnover : 0
          const wagerLeft = turnoverAmount - totalTurnover

          // Format: ₺1000 • ₺750 (₺250)
          balanceMemberSince.textContent = `Çevrim Sarti: ₺${turnoverAmount.toFixed(2)} • Oynanan: ₺${wagerLeft.toFixed(
            2
          )} (Kalan: ₺${totalTurnover.toFixed(2)})`
        } else {
          // Display VIP status when no bonus
          let vip = ''
          if (customer.odin.vip) {
            vip = customer.odin.vip
            console.log('VIP STATUS:', vip)
            if (vip) {
              vip = 'VIP'
            } else {
              vip = 'ÜYE'
            }
          }
          balanceMemberSince.textContent = `VIP statüsü: ${vip || '-'}`
        }
      }

      // Show/hide balance negative warning
      const balanceNegativeWarning = document.getElementById('balance-negative-warning')
      if (balanceNegativeWarning) {
        if (customer.odin.balance !== undefined && Number(customer.odin.balance) < 0) {
          balanceNegativeWarning.style.display = ''
        } else {
          balanceNegativeWarning.style.display = 'none'
        }
      }

      displayLastPlayedSlot(lastPlayedSlot)

      // Switch content visibility
      defaultContent.style.display = 'none'
      userContent.style.display = 'flex'

      // Responsive badge observer (call after badge is in DOM)
      setupProfileBadgeResponsive()

      // Attach roulette button event after rendering
      const randomBtn = document.getElementById('random-slot-btn')
      if (randomBtn) {
        randomBtn.onclick = () => startSlotRoulette(slots)
      }

      console.log('User content updated successfully')
    }

    function showDefaultContent() {
      const defaultContent = document.getElementById('cta-content-default')
      const userContent = document.getElementById('cta-content-user')

      if (defaultContent && userContent) {
        defaultContent.style.display = 'flex'
        userContent.style.display = 'none'
        // Hide last played slot for non-logged users
        hideLastPlayedSlot()
        console.log('Showing default content for non-logged users')
      }
    }

    function setupCTANavigationHandlers() {
      // Handle all buttons with data-navigate attribute
      const ctaButtons = document.querySelectorAll('.hero-cta-button[data-navigate]')
      const bonusDepositBtn = document.getElementById('bonus-deposit-btn')

      const cleanups = []

      const handleBonusDepositClick = () => {
        document.querySelector('.bonus_demand')?.click()
      }

      bonusDepositBtn?.addEventListener('click', handleBonusDepositClick)
      cleanups.push(() => bonusDepositBtn?.removeEventListener('click', handleBonusDepositClick))

      ctaButtons.forEach(button => {
        let isScrolling = false
        let touchStartY = 0

        const handleNavigation = e => {
          e.preventDefault()
          const path = button.getAttribute('data-navigate')

          if (path && window.$LANDING && window.$LANDING.navigate) {
            window.$LANDING.navigate(path)
          } else if (path) {
            window.location.href = path
          }
        }

        // Mouse click
        button.addEventListener('click', handleNavigation)
        cleanups.push(() => button.removeEventListener('click', handleNavigation))

        // Touch support with scroll detection
        const touchStartHandler = e => {
          isScrolling = false
          touchStartY = e.touches[0].clientY
        }
        const touchMoveHandler = e => {
          const touchMoveY = e.touches[0].clientY
          const deltaY = Math.abs(touchMoveY - touchStartY)

          if (deltaY > 10) {
            isScrolling = true
          }
        }
        const touchEndHandler = e => {
          if (isScrolling) {
            return
          }

          e.preventDefault()
          handleNavigation(e)
        }
        button.addEventListener('touchstart', touchStartHandler)
        button.addEventListener('touchmove', touchMoveHandler)
        button.addEventListener('touchend', touchEndHandler)

        cleanups.push(() => {
          button.removeEventListener('touchstart', touchStartHandler)
          button.removeEventListener('touchmove', touchMoveHandler)
          button.removeEventListener('touchend', touchEndHandler)
        })
      })

      return () => {
        cleanups.forEach(cb => cb())
      }
    }

    // Responsive profile badge setup
    function setupProfileBadgeResponsive() {
      const ctaCol = document.querySelector('.hero-cta-column')
      const badge = document.querySelector('.profile-rank-badge')

      if (!ctaCol || !badge) return

      const observer = new ResizeObserver(entries => {
        for (let entry of entries) {
          const width = entry.contentRect.width
          if (width <= 400) {
            badge.classList.add('profile-badge-mobile')
          } else {
            badge.classList.remove('profile-badge-mobile')
          }
        }
      })

      observer.observe(ctaCol)
    }

    // Slot roulette animation functionality
    async function startSlotRoulette(slots) {
      // Initial setup
      const btn = document.getElementById('random-slot-btn')
      const roulette = document.getElementById('roulette-animation')
      const lastPlayedGameBox = document.getElementById('last-played-game')
      if (!btn || !roulette || !lastPlayedGameBox) return

      // UI state changes
      btn.disabled = true
      btn.style.opacity = 0.7
      lastPlayedGameBox.style.display = 'none'
      roulette.style.display = 'flex'

      if (!slots || slots.length === 0) {
        console.log('No slots available for roulette')
        // Fallback if no slots available
        btn.disabled = false
        btn.style.opacity = 1
        lastPlayedGameBox.style.display = 'flex'
        roulette.style.display = 'none'
        return
      }

      console.log('Starting roulette with', slots.length, 'available slots')

      // Configuration constants
      const SLOT_WIDTH = 64 // Exact width of one slot card in pixels
      const TOTAL_SLOTS = Math.min(200, slots.length * 3) // Total slots in the roulette (repeat slots if needed)

      // Pre-select the winning slot
      const winningSlot = slots[Math.floor(Math.random() * slots.length)]
      const winningIndex = Math.floor(TOTAL_SLOTS * 0.7) // Place winner at 70% through the strip

      // Shuffle and select slots, repeat if we don't have enough
      const shuffledSlots = [...slots].slice(0, 30).sort(() => Math.random() - 0.5)
      const slotsToShow = []

      // Fill the roulette with slots, repeating if necessary
      for (let i = 0; i < TOTAL_SLOTS; i++) {
        if (i === winningIndex) {
          // Place the pre-selected winning slot at the winning position
          slotsToShow.push(winningSlot)
        } else {
          const slotIndex = i % shuffledSlots.length
          const slot = shuffledSlots[slotIndex]
          if (slot) {
            slotsToShow.push(slot)
          }
        }
      }
      console.log(slotsToShow)
      console.log('Created roulette with', slotsToShow.length, 'slot cards, winner at index', winningIndex)

      // Build roulette strip
      const strip = document.createElement('div')
      strip.className = 'roulette-strip'
      strip.style.width = `${TOTAL_SLOTS * SLOT_WIDTH}px`

      slotsToShow.forEach(slot => {
        const card = document.createElement('div')
        card.className = 'roulette-card'

        // Create image element with error handling
        const img = document.createElement('img')
        const gameId = slot.gameId || slot.id
        const gameName = slot.gameName || slot.name || 'Unknown Game'

        img.src = getGameImageUrl(gameId)
        img.alt = gameName
        img.loading = 'lazy'

        // Add error handling for missing images
        img.onerror = function () {
          // Try alternative image paths
          const fallbackUrls = [
            `${window.location.origin}/cdn/common/assets/images/casino/150x150/${gameId}.jpg`,
            `${window.location.origin}/cdn/common/assets/images/casino/100x100/${gameId}.jpg`,
            `${window.location.origin}/cdn/common/assets/images/casino/default.jpg`
          ]

          let currentFallback = 0
          const tryNextFallback = () => {
            if (currentFallback < fallbackUrls.length) {
              this.src = fallbackUrls[currentFallback]
              currentFallback++
            } else {
              // If all fallbacks fail, hide the image and show a placeholder
              this.style.display = 'none'
              const placeholder = document.createElement('div')
              placeholder.className = 'game-placeholder'
              placeholder.textContent = '🎰'
              placeholder.style.cssText =
                'width: 100%; height: 38px; display: flex; align-items: center; justify-content: center; background: rgba(255,255,255,0.1); border-radius: 6px; font-size: 20px;'
              this.parentNode.insertBefore(placeholder, this)
            }
          }

          this.onerror = tryNextFallback
          tryNextFallback()
        }

        // Append only the image to card (no name)
        card.appendChild(img)
        strip.appendChild(card)
      })

      // Clear and setup roulette container
      roulette.innerHTML = ''
      roulette.appendChild(strip)
      roulette.appendChild(document.createElement('div')).className = 'roulette-indicator'

      const rouletteWidth = roulette.offsetWidth

      // Calculate exact position to center the winning slot
      const winningSlotPosition = winningIndex * SLOT_WIDTH
      const finalTranslateX = winningSlotPosition - SLOT_WIDTH * 2 + (Math.random() * 20 - 10)

      // Ensure we don't go out of bounds
      const maxTranslateX = TOTAL_SLOTS * SLOT_WIDTH - rouletteWidth
      const clampedTranslateX = Math.max(0, Math.min(finalTranslateX, maxTranslateX))

      // Animation parameters
      const duration = 2000 + Math.random() * 1000
      let startTime = null

      function easeOutCubic(t) {
        return 1 - Math.pow(1 - t, 3)
      }

      function animateRoulette(timestamp) {
        if (!startTime) startTime = timestamp
        const elapsed = timestamp - startTime
        const progress = Math.min(elapsed / duration, 1)
        const easedProgress = easeOutCubic(progress)
        const currentTranslateX = easedProgress * clampedTranslateX

        strip.style.transform = `translateX(-${currentTranslateX}px)`

        if (progress < 1) {
          requestAnimationFrame(animateRoulette)
        } else {
          // The winning slot should now be centered
          console.log('[ROULETTE] Stopped on:', winningSlot.name, `at index ${winningIndex}`)
          console.log('[ROULETTE] Final position:', clampedTranslateX, 'px')

          // Update last played slot with the pre-selected winning game
          setTimeout(() => {
            displayLastPlayedSlot(winningSlot)
            roulette.style.display = 'none'
            lastPlayedGameBox.style.display = 'flex'
            btn.disabled = false
            btn.style.opacity = 1
          }, 500)
        }
      }

      requestAnimationFrame(animateRoulette)
    }

    // Periodic login status checker
    let currentLoginState = isLoggedIn()
    let loginCheckInterval
    let cleanup = null

    function startLoginStatusChecker() {
      loginCheckInterval = setInterval(async () => {
        const newLoginState = isLoggedIn()

        if (newLoginState !== currentLoginState) {
          console.log('Login state changed:', currentLoginState, '->', newLoginState)
          currentLoginState = newLoginState

          // Refresh the view based on new login state
          if (newLoginState) {
            // User just logged in - fetch data and show user content
            cleanup?.()
            cleanup = await init()
          }
        }
      }, 2000)
    }

    function stopLoginStatusChecker() {
      if (loginCheckInterval) {
        clearInterval(loginCheckInterval)
        loginCheckInterval = null
      }
    }

    // Initialize when the component mounts
    init().then(cb => {
      cleanup = cb
    })

    // Start periodic login status checking
    startLoginStatusChecker()

    // Cleanup function (if needed for unmounting)
    window.addEventListener('beforeunload', stopLoginStatusChecker)

    return () => {
      cleanup?.()
      stopLoginStatusChecker()
    }
  }
})
