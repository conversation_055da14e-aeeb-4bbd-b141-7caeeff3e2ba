/* Tournaments Section Styles */
.tournaments-section {
  padding: 60px 0;
  background: transparent;
  position: relative;
  overflow: hidden;
}

.tournaments-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
  position: relative;
  z-index: 2;
}

.tournaments-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;
}

.tournaments-slider {
  position: relative;
  overflow: hidden;
  border-radius: 16px;
}

.tournaments-track {
  display: flex;
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  width: 100%;
  gap: 0;
  cursor: grab;
  user-select: none;
}

.tournaments-track:active {
  cursor: grabbing;
}

/* Tournament Card Styles */
.tournaments-section .tournament-card {
  flex: 0 0 100%;
  width: 100%;
  max-height: 500px;
  height: 500px;
  background: linear-gradient(145deg,
      rgba(20, 20, 35, 0.98) 0%,
      rgba(15, 15, 25, 1) 50%,
      rgba(20, 20, 35, 0.98) 100%);
  border-radius: 24px;
  border: 2px solid rgba(var(--primary-color), 0.2);
  overflow: hidden;
  position: relative;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(20px);
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.6),
    0 8px 24px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.08);
}

.tournaments-section .tournament-card-inner {
  display: flex;
  align-items: stretch;
  height: 100%;
}

/* Info Column - 2/5 of space */
.tournaments-section .tournament-info-column {
  flex: 0 0 40%;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  position: relative;
  background: linear-gradient(135deg,
      rgba(15, 15, 25, 0.95) 0%,
      rgba(10, 10, 20, 1) 100%);
  border-right: 2px solid rgba(var(--primary-color), 0.15);
}

/* Image Column - 3/5 of space */
.tournaments-section .tournament-image-column {
  flex: 0 0 60%;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tournaments-section .tournament-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  transition: all 0.4s ease;
  filter: brightness(0.9) contrast(1.1);
  aspect-ratio: 900 / 450;
}

/* Tournament Image Gradient Overlay */
.tournaments-section .tournament-image-column::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(60deg, rgba(20, 20, 30, 1) 0%, rgba(20, 20, 30, 1) 40%, transparent 60%);
  pointer-events: none;
  z-index: 1;
}

/* Image hover effects removed */

/* Status and Countdown Container */
.tournaments-section .tournament-status-container {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 4px;
}

/* Tournament Status Styles */
.tournaments-section .tournament-status {
  padding: 10px 16px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
  text-align: center;
  flex-shrink: 0;
}

/* Countdown Container */
.tournaments-section .tournament-countdown-container {
  display: flex;
  align-items: center;
  gap: 4px;
  font-family: 'Courier New', monospace;
  background: rgba(0, 0, 0, 0.3);
  padding: 8px 12px;
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.tournaments-section .countdown-unit {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 32px;
}

.tournaments-section .countdown-value {
  font-size: 1rem;
  font-weight: 900;
  color: rgb(var(--primary-color));
  line-height: 1;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.tournaments-section .countdown-label {
  font-size: 0.6rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.7);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: 2px;
  line-height: 1;
}

.countdown-separator {
  font-size: 1.2rem;
  font-weight: 900;
  color: rgba(255, 255, 255, 0.5);
  margin: 0 2px;
  line-height: 1;
}

.tournament-status.ongoing {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  color: white;
  box-shadow:
    0 0 25px rgba(34, 197, 94, 0.4),
    0 4px 12px rgba(34, 197, 94, 0.3);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.tournament-status.countdown {
  background: linear-gradient(135deg, rgb(var(--primary-color)) 0%, rgba(var(--primary-color), 0.8) 100%);
  color: rgba(15, 15, 25, 0.95);
  box-shadow:
    0 0 25px rgba(var(--primary-color), 0.4),
    0 4px 12px rgba(var(--primary-color), 0.3);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  animation: pulse-countdown 2s infinite;
}

.tournament-status.ended {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  box-shadow:
    0 0 25px rgba(239, 68, 68, 0.4),
    0 4px 12px rgba(239, 68, 68, 0.3);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

@keyframes pulse-countdown {

  0%,
  100% {
    transform: scale(1);
    box-shadow:
      0 0 25px rgba(var(--primary-color), 0.4),
      0 4px 12px rgba(var(--primary-color), 0.3);
  }

  50% {
    transform: scale(1.02);
    box-shadow:
      0 0 35px rgba(var(--primary-color), 0.6),
      0 6px 16px rgba(var(--primary-color), 0.4);
  }
}

.tournament-name {
  font-size: 1.4rem;
  font-weight: 800;
  color: rgb(var(--primary-color));
  line-height: 1.2;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin: 0;
}

.tournament-prize-pool {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  background: linear-gradient(135deg, rgba(var(--primary-color), 0.2) 0%, rgba(var(--primary-color), 0.1) 100%);
  border-radius: 8px;
  border: 2px solid rgba(var(--primary-color), 0.4);
  box-shadow: 0 4px 12px rgba(var(--primary-color), 0.2);
  gap: 4px;
}

.prize-pool-label {
  font-size: 0.65rem;
  font-weight: 700;
  color: rgb(var(--primary-color));
  /* color: rgba(15, 15, 25, 0.95); */
  /* background: linear-gradient(135deg, rgb(var(--primary-color)) 0%, rgba(var(--primary-color), 0.9) 100%); */
  padding: 0px 8px;
  border-radius: 8px;
  text-transform: uppercase;
  letter-spacing: 0.6px;
  line-height: 1;
  /* text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2); */
  /* box-shadow: 0 2px 6px rgba(var(--primary-color), 0.4); */
  /* border: 1px solid rgba(var(--primary-color), 0.6); */
}

.prize-pool-amount {
  font-size: 1.2rem;
  font-weight: 900;
  color: rgb(var(--primary-color));
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  filter: drop-shadow(0 0 8px rgba(var(--primary-color), 0.5));
  line-height: 1;
}

@keyframes prize-glow {

  0%,
  100% {
    box-shadow:
      0 0 20px rgba(var(--primary-color), 0.4),
      0 4px 12px rgba(var(--primary-color), 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  50% {
    box-shadow:
      0 0 30px rgba(var(--primary-color), 0.6),
      0 6px 16px rgba(var(--primary-color), 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }
}

/* Trophy emoji moved to JavaScript for better control */

.tournament-header-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(var(--primary-color), 0.1);
  margin-bottom: 12px;
}

/* Tabbed View Styles */
.tournament-tabs-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  height: 100%;
  /* Use full available height */
}

.tournament-tabs {
  display: flex;
  border-bottom: 2px solid rgba(var(--primary-color), 0.3);
  margin-bottom: 8px;
  background: rgba(var(--secondary-color), 0.3);
  border-radius: 12px 12px 0 0;
  overflow: hidden;
}

.tournament-tab {
  flex: 1;
  padding: 8px 12px;
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.7rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-bottom: 3px solid transparent;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  position: relative;
  overflow: hidden;
}

.tournament-tab::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(var(--primary-color), 0.2), transparent);
  transition: left 0.5s ease;
}

.tournament-tab:hover::before {
  left: 100%;
}

.tournament-tab:hover {
  color: rgba(255, 255, 255, 0.95);
  background: rgba(var(--primary-color), 0.15);
  transform: translateY(-1px);
}

.tournament-tab.active {
  color: rgb(var(--primary-color));
  border-bottom-color: rgb(var(--primary-color));
  background: linear-gradient(135deg, rgba(var(--primary-color), 0.2) 0%, rgba(var(--primary-color), 0.1) 100%);
  box-shadow:
    0 2px 8px rgba(var(--primary-color), 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.tournament-tab-icon {
  font-size: 1rem;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.tournament-tab-label {
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.tournament-tab-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  height: calc(100% - 50px);
  /* Account for tab height */
}

.tournament-tab-panel {
  display: none;
  flex: 1;
  height: 100%;
  overflow: hidden;
}

.tournament-tab-panel.active {
  display: flex;
  flex-direction: column;
}

.tournament-tab-panel-inner {
  flex: 1;
  height: 100%;
  overflow-y: auto;
  padding-right: 4px;
  scrollbar-width: thin;
  scrollbar-color: rgba(var(--primary-color), 0.5) transparent;
}

.tournament-tab-panel-inner::-webkit-scrollbar {
  width: 6px;
}

.tournament-tab-panel-inner::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.tournament-tab-panel-inner::-webkit-scrollbar-thumb {
  background: rgba(var(--primary-color), 0.5);
  border-radius: 3px;
  transition: background 0.3s ease;
}

.tournament-tab-panel-inner::-webkit-scrollbar-thumb:hover {
  background: rgba(var(--primary-color), 0.7);
}

/* Games Section - Header removed */

.tournament-games-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  flex: 1;
  min-height: 200px;
  /* Ensure minimum height even when empty */
  padding-top: 8px;
  /* Add some top spacing since header is removed */
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: thin;
  scrollbar-color: rgba(var(--primary-color), 0.5) transparent;
}

.tournament-games-list::-webkit-scrollbar {
  width: 6px;
}

.tournament-games-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.tournament-games-list::-webkit-scrollbar-thumb {
  background: rgba(var(--primary-color), 0.5);
  border-radius: 3px;
  transition: background 0.3s ease;
}

.tournament-games-list::-webkit-scrollbar-thumb:hover {
  background: rgba(var(--primary-color), 0.7);
}

.tournament-empty-games {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 40px 20px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.03) 0%, rgba(255, 255, 255, 0.01) 100%);
  border-radius: 16px;
  border: 2px dashed rgba(255, 255, 255, 0.1);
  min-height: 200px;
  /* Ensure full height usage */
}

.tournament-empty-icon {
  font-size: 3rem;
  margin-bottom: 16px;
  opacity: 0.6;
}

.tournament-empty-content h4 {
  font-size: 1.1rem;
  font-weight: 700;
  color: rgba(255, 255, 255, 0.9);
  margin: 0 0 8px 0;
}

.tournament-empty-content p {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0 0 4px 0;
}

.tournament-empty-content span {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.5);
  font-style: italic;
}

.tournament-game-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);
  border-radius: 12px;
  border: 1px solid rgba(var(--primary-color), 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
  /* Prevent items from shrinking */
}

.tournament-game-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(var(--primary-color), 0.1), transparent);
  transition: left 0.5s ease;
}

.tournament-game-item:hover::before {
  left: 100%;
}

.tournament-game-item:hover {
  background: linear-gradient(135deg, rgba(var(--primary-color), 0.15) 0%, rgba(var(--primary-color), 0.08) 100%);
  border-color: rgba(var(--primary-color), 0.4);
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(var(--primary-color), 0.2);
}

.tournament-game-rank {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, rgb(var(--primary-color)) 0%, rgba(var(--primary-color), 0.8) 100%);
  color: rgb(var(--secondary-color));
  font-size: 0.7rem;
  font-weight: 700;
  border-radius: 50%;
  flex-shrink: 0;
  box-shadow: 0 2px 6px rgba(var(--primary-color), 0.3);
}

.tournament-game-image-container {
  position: relative;
  flex-shrink: 0;
}

.tournament-game-image {
  width: 40px;
  height: 30px;
  border-radius: 6px;
  object-fit: cover;
  border: 1px solid rgba(var(--primary-color), 0.3);
  transition: all 0.3s ease;
}

.tournament-game-item:hover .tournament-game-image {
  transform: scale(1.05);
  border-color: rgba(var(--primary-color), 0.5);
}

.tournament-game-provider {
  display: inline-flex;
  align-items: center;
  padding: 2px 6px;
  background: linear-gradient(135deg, rgba(var(--primary-color), 0.2) 0%, rgba(var(--primary-color), 0.1) 100%);
  border: 1px solid rgba(var(--primary-color), 0.3);
  border-radius: 4px;
  font-size: 0.65rem;
  font-weight: 700;
  color: rgb(var(--primary-color));
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  box-shadow: 0 1px 3px rgba(var(--primary-color), 0.2);
}

.tournament-game-info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.tournament-game-name {
  font-size: 0.8rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.95);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.tournament-game-meta {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.65rem;
}

.tournament-game-type {
  color: rgb(var(--primary-color));
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.tournament-game-dot {
  color: rgba(255, 255, 255, 0.4);
  font-weight: 700;
}

.tournament-game-status {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #22c55e;
  font-weight: 600;
}

.tournament-game-status-dot {
  width: 6px;
  height: 6px;
  background: #22c55e;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

.tournament-game-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.tournament-game-play-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, rgb(var(--primary-color)) 0%, rgba(var(--primary-color), 0.8) 100%);
  color: rgb(var(--secondary-color));
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.8rem;
  font-weight: 700;
  box-shadow: 0 2px 6px rgba(var(--primary-color), 0.3);
}

.tournament-game-play-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(var(--primary-color), 0.4);
}

/* Action Buttons */
.tournament-actions {
  display: flex;
  margin-top: auto;
  flex-shrink: 0;
}

.tournament-btn {
  flex: 1;
  padding: 16px;
  border: none;
  border-radius: 0;
  font-size: 0.8rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.tournament-btn:first-child {
  border-radius: 0 0 0 22px;
}

.tournament-btn:last-child {
  border-radius: 0 0 22px 0;
}

.tournament-btn-primary {
  background: linear-gradient(135deg, rgb(var(--primary-color)) 0%, rgba(var(--primary-color), 0.8) 100%);
  color: rgb(var(--secondary-color));
  box-shadow: 0 4px 12px rgba(var(--primary-color), 0.3);
}

.tournament-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(var(--primary-color), 0.4);
  background: linear-gradient(135deg, rgba(var(--primary-color), 1.1) 0%, rgb(var(--primary-color)) 100%);
}

.tournament-btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.tournament-btn-secondary:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.tournament-leaderboard-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg,
      rgba(var(--primary-color), 0.08) 0%,
      rgba(var(--primary-color), 0.04) 100%);
  padding: 16px;
  border-radius: 12px;
  border: 1px solid rgba(var(--primary-color), 0.15);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  min-height: 200px;
  /* Ensure full height usage */
}

/* New Leaderboard Styles */
.tournament-leaderboard {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 200px;
}

.leaderboard-header {
  display: grid;
  grid-template-columns: 60px 1fr 80px 100px;
  gap: 12px;
  padding: 8px 16px;
  background: linear-gradient(135deg, rgba(var(--primary-color), 0.2) 0%, rgba(var(--primary-color), 0.1) 100%);
  border-radius: 8px;
  margin-bottom: 4px;
  font-size: 0.75rem;
  font-weight: 700;
  color: rgb(var(--primary-color));
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.leaderboard-header-position,
.leaderboard-header-username,
.leaderboard-header-points,
.leaderboard-header-prize {
  text-align: center;
}

.leaderboard-header-username {
  text-align: left;
}

.leaderboard-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.leaderboard-row {
  display: grid;
  grid-template-columns: 60px 1fr 80px 100px;
  gap: 12px;
  align-items: center;
  padding: 6px 16px;
  background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.08) 0%,
      rgba(255, 255, 255, 0.04) 100%);
  border: 1px solid rgba(var(--primary-color), 0.2);
  border-radius: 6px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.leaderboard-row::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(var(--primary-color), 0.1), transparent);
  transition: left 0.5s ease;
}

.leaderboard-row:hover::before {
  left: 100%;
}

.leaderboard-row:hover {
  background: linear-gradient(135deg,
      rgba(var(--primary-color), 0.15) 0%,
      rgba(var(--primary-color), 0.08) 100%);
  border-color: rgba(var(--primary-color), 0.4);
  transform: translateX(2px);
  box-shadow: 0 4px 12px rgba(var(--primary-color), 0.2);
}

.leaderboard-position {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background: linear-gradient(135deg, rgb(var(--primary-color)) 0%, rgba(var(--primary-color), 0.8) 100%);
  color: rgb(var(--secondary-color));
  font-size: 0.75rem;
  font-weight: 700;
  border-radius: 50%;
  margin: 0 auto;
  box-shadow: 0 2px 6px rgba(var(--primary-color), 0.3);
}

.leaderboard-username {
  font-size: 0.85rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.95);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: left;
}

.leaderboard-points {
  font-size: 0.8rem;
  font-weight: 600;
  color: rgb(var(--primary-color));
  text-align: center;
}

.leaderboard-prize {
  font-size: 0.8rem;
  font-weight: 700;
  color: rgb(var(--primary-color));
  text-align: center;
  background: rgba(var(--primary-color), 0.1);
  padding: 4px 8px;
  border-radius: 6px;
  border: 1px solid rgba(var(--primary-color), 0.3);
}

.leaderboard-row:first-child {
  background: linear-gradient(135deg,
      rgba(255, 215, 0, 0.15) 0%,
      rgba(255, 215, 0, 0.08) 100%);
  border-color: rgba(255, 215, 0, 0.4);
}

.leaderboard-row:first-child .leaderboard-position {
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  color: #1a1a1a;
  box-shadow: 0 2px 6px rgba(255, 215, 0, 0.4);
}

.leaderboard-rank {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background: linear-gradient(135deg, rgb(var(--primary-color)) 0%, rgba(var(--primary-color), 0.8) 100%);
  color: rgb(var(--secondary-color));
  font-weight: 700;
  font-size: 0.75rem;
  border-radius: 50%;
  flex-shrink: 0;
  box-shadow: 0 2px 6px rgba(var(--primary-color), 0.3);
  margin-right: 12px;
}

.leaderboard-item:first-child .leaderboard-rank {
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  color: #1a1a1a;
  box-shadow: 0 2px 6px rgba(255, 215, 0, 0.4);
}

.leaderboard-item:nth-child(2) .leaderboard-rank {
  background: linear-gradient(135deg, #c0c0c0 0%, #e5e5e5 100%);
  color: #1a1a1a;
  box-shadow: 0 2px 6px rgba(192, 192, 192, 0.4);
}

.leaderboard-item:nth-child(3) .leaderboard-rank {
  background: linear-gradient(135deg, #cd7f32 0%, #daa520 100%);
  color: #1a1a1a;
  box-shadow: 0 2px 6px rgba(205, 127, 50, 0.4);
}

/* Empty leaderboard row styling */
.leaderboard-row-empty {
  opacity: 0.4;
  background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.03) 0%,
      rgba(255, 255, 255, 0.01) 100%) !important;
  border: 1px dashed rgba(255, 255, 255, 0.1) !important;
}

.leaderboard-row-empty:hover {
  background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.05) 0%,
      rgba(255, 255, 255, 0.02) 100%) !important;
  border-color: rgba(255, 255, 255, 0.15) !important;
  transform: none !important;
  box-shadow: none !important;
}

.leaderboard-row-empty .leaderboard-position {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%) !important;
  color: rgba(255, 255, 255, 0.3) !important;
  box-shadow: none !important;
}

.leaderboard-row-empty .leaderboard-username,
.leaderboard-row-empty .leaderboard-points,
.leaderboard-row-empty .leaderboard-prize {
  color: rgba(255, 255, 255, 0.2) !important;
  background: transparent !important;
  border: none !important;
}

.leaderboard-player {
  flex: 1;
  color: rgba(255, 255, 255, 0.95);
  font-size: 0.8rem;
  font-weight: 600;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.leaderboard-reward {
  color: rgb(var(--primary-color));
  font-weight: 700;
  font-size: 0.8rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  background: rgba(var(--primary-color), 0.1);
  padding: 4px 8px;
  border-radius: 8px;
  border: 1px solid rgba(var(--primary-color), 0.3);
}

.tournament-empty-leaderboard {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 40px 20px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.03) 0%, rgba(255, 255, 255, 0.01) 100%);
  border-radius: 16px;
  border: 2px dashed rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.5);
  font-size: 0.85rem;
  font-style: italic;
  min-height: 150px;
  /* Ensure full height usage */
}

/* Loading Placeholder Styles */
.tournament-card.loading-placeholder {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.tournament-card.loading-placeholder .tournament-card-inner {
  display: flex;
  align-items: stretch;
}

.tournaments-section .tournament-image-placeholder {
  flex: 0 0 60%;
  background: linear-gradient(90deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.2) 50%,
      rgba(255, 255, 255, 0.1) 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.tournament-content-placeholder {
  flex: 0 0 40%;
  padding: 28px;
  display: flex;
  flex-direction: column;
  gap: 14px;
}

.tournament-title-placeholder {
  height: 32px;
  background: linear-gradient(90deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.2) 50%,
      rgba(255, 255, 255, 0.1) 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
  width: 80%;
}

.tournament-info-placeholder {
  height: 120px;
  background: linear-gradient(90deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.2) 50%,
      rgba(255, 255, 255, 0.1) 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }

  100% {
    background-position: 200% 0;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .tournaments-section {
    padding: 40px 0;
  }

  .tournaments-container {
    padding: 0 16px;
  }

  .tournaments-header {
    margin-bottom: 24px;
  }

  .tournament-card {
    max-height: none;
    height: auto;
    /* Allow flexible height on mobile */
  }

  .tournament-card-inner {
    flex-direction: column;
    height: auto;
  }

  .tournaments-section .tournament-image-column {
    flex: 0 0 120px;
    order: -1;
  }

  .tournament-info-column {
    flex: 1;
    padding: 16px;
    order: 1;
    gap: 12px;
  }



  .tournament-header-section {
    gap: 6px;
    padding-bottom: 8px;
  }

  .tournament-status-container {
    gap: 8px;
    /* Keep flex-direction: row for same row layout */
  }

  .tournament-status {
    padding: 8px 12px;
    font-size: 0.7rem;
  }

  .tournament-countdown-container {
    padding: 6px 8px;
    gap: 2px;
  }

  .countdown-unit {
    min-width: 24px;
  }

  .countdown-value {
    font-size: 0.8rem;
  }

  .countdown-label {
    font-size: 0.5rem;
  }

  .countdown-separator {
    font-size: 0.9rem;
    margin: 0 1px;
  }

  .tournament-name {
    font-size: 1.1rem;
  }

  .tournament-prize-pool {
    padding: 6px 12px;
    gap: 3px;
  }

  .prize-pool-label {
    font-size: 0.55rem;
    padding: 3px 6px;
    border-radius: 6px;
  }

  .prize-pool-amount {
    font-size: 1rem;
  }

  .leaderboard-header {
    grid-template-columns: 40px 1fr 60px 80px;
    gap: 8px;
    padding: 6px 12px;
    font-size: 0.65rem;
  }

  .leaderboard-row {
    grid-template-columns: 40px 1fr 60px 80px;
    gap: 8px;
    padding: 4px 12px;
  }

  .leaderboard-position {
    width: 22px;
    height: 22px;
    font-size: 0.65rem;
  }

  .leaderboard-username {
    font-size: 0.75rem;
  }

  .leaderboard-points {
    font-size: 0.7rem;
  }

  .leaderboard-prize {
    font-size: 0.7rem;
    padding: 2px 4px;
  }

  .tournament-description {
    font-size: 0.8rem;
    -webkit-line-clamp: 2;
    padding: 8px 10px;
  }

  .tournament-tabs-container {
    height: 100%;
    /* Use full available height on mobile */
  }

  .tournament-tabs {
    margin-bottom: 12px;
  }

  .tournament-tab {
    padding: 10px 12px;
    font-size: 0.7rem;
    gap: 6px;
  }

  .tournament-tab-icon {
    font-size: 0.9rem;
  }

  /* Games header removed - no mobile styles needed */

  .tournament-tab-content {
    height: calc(100% - 60px);
    /* Account for tab height on mobile */
  }

  .tournament-tab-panel-inner {
    max-height: 250px;
    /* Limit height on mobile */
    overflow-y: auto;
    overflow-x: hidden;
  }

  .tournament-games-list {
    max-height: 200px;
    overflow-y: auto;
    overflow-x: hidden;
    scrollbar-width: thin;
    scrollbar-color: rgba(var(--primary-color), 0.5) transparent;
    /* Remove flex: 1 to prevent overflow */
    flex: none;
  }

  .tournament-games-list::-webkit-scrollbar {
    width: 4px;
  }

  .tournament-games-list::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 2px;
  }

  .tournament-games-list::-webkit-scrollbar-thumb {
    background: rgba(var(--primary-color), 0.5);
    border-radius: 2px;
    transition: background 0.3s ease;
  }

  .tournament-games-list::-webkit-scrollbar-thumb:hover {
    background: rgba(var(--primary-color), 0.7);
  }

  .tournament-game-item {
    padding: 8px;
    gap: 8px;
    flex-shrink: 0;
    /* Prevent items from shrinking */
  }

  .tournament-game-rank {
    width: 20px;
    height: 20px;
    font-size: 0.65rem;
  }

  .tournament-game-image {
    width: 32px;
    height: 24px;
  }

  .tournament-game-name {
    font-size: 0.75rem;
  }

  .tournament-game-meta {
    font-size: 0.6rem;
  }

  .tournament-game-provider {
    font-size: 0.55rem;
    padding: 1px 4px;
  }

  .tournament-game-play-btn {
    width: 28px;
    height: 28px;
    font-size: 0.7rem;
  }

  .leaderboard-item {
    padding: 8px 10px;
  }

  .leaderboard-rank {
    width: 24px;
    height: 24px;
    font-size: 0.7rem;
    margin-right: 10px;
  }

  .leaderboard-player {
    font-size: 0.75rem;
  }

  .leaderboard-reward {
    font-size: 0.75rem;
    padding: 3px 6px;
  }

  .tournament-btn {
    padding: 12px;
    font-size: 0.75rem;
  }

  .leaderboard-item {
    padding: 4px 8px;
  }

  .leaderboard-rank,
  .leaderboard-player,
  .leaderboard-reward {
    font-size: 0.65rem;
  }

  .tournament-leaderboard-content {
    padding: 12px;
  }
}


.tournaments-section .tournament-image-text-container {
  overflow: auto;
  height: 100%;
}
