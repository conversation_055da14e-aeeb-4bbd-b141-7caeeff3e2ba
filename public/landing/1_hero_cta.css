/* Hero CTA Column Styles */

/* CTA Column (1/4) */
.hero-cta-column {
  position: relative;
  background:
    linear-gradient(135deg, rgb(35 35 63 / 95%) 0%, rgb(38 38 69 / 85%) 100%), linear-gradient(45deg, rgb(46 40 21 / 3%) 0%, transparent 50%);
  border-radius: 24px;
  border: 1px solid rgba(var(--primary-color), 0.1);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.25),
    0 4px 12px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  overflow: hidden;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  width: clamp(400px, 100%) !important;
  max-width: 400px !important;
}

/* CTA Background Effects */
.hero-cta-column::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    /* Spaced grid pattern */
    linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px),
    linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
    /* Subtle gradient squares pattern with more spacing */
    repeating-linear-gradient(45deg,
      rgba(30, 30, 50, 0.2) 0px,
      rgba(30, 30, 50, 0.2) 1px,
      transparent 1px,
      transparent 12px),
    /* Base gradient overlay */
    linear-gradient(135deg, rgba(20, 25, 45, 0.3) 0%, rgba(35, 40, 65, 0.2) 50%, rgba(25, 30, 50, 0.3) 100%);
  background-size: 48px 48px, 48px 48px, 16px 16px, 100% 100%;
  pointer-events: none;
  z-index: 1;
  opacity: 0.6;
}

.hero-cta-column .cta-content {
  position: relative;
  z-index: 4;
  text-align: center;
  height: 100%;
  max-height: 400px;
  min-height: 0;
  display: flex;
  flex-direction: column;
  justify-content: stretch;
  gap: 12px;
  padding: 8px;
}

/* Specific styling for default content (non-logged-in users) */
.hero-cta-column #cta-content-default {
  justify-content: center;
  align-items: center;
  padding: 16px;
  gap: 16px;
}

/* Specific styling for user content (logged-in users) */
.hero-cta-column .user-content {
  justify-content: flex-start;
  align-items: stretch;
  text-align: left;
  position: relative;
  z-index: 4;
  height: 100%;
  max-height: 100%;
  min-height: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 10px;
  padding: 16px;
}

.hero-cta-column .hero-title {
  font-size: 2.4rem;
  font-weight: 800;
  color: rgb(var(--primary-color));
  margin: 0;
  text-shadow:
    0 2px 4px rgba(0, 0, 0, 0.4),
    0 0 20px rgba(var(--primary-color), 0.3);
  line-height: 1.1;
  letter-spacing: -0.02em;
}

.hero-cta-column .hero-description {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  line-height: 1.5;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.hero-cta-column .hero-cta-button {
  background: linear-gradient(135deg, #FFD700 0%, #ffe42d 100%);
  border: none;
  color: #140608;
  padding: 18px 36px;
  border-radius: 16px;
  font-size: 1.2rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow:
    0 3px 15px rgba(var(--primary-color), 0.35),
    0 2px 8px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  text-decoration: none;
  font-family: 'Montserrat', sans-serif;
  box-shadow: 0 4px 16px rgba(var(--primary-color), 0.3);
  position: relative;
  overflow: hidden;
}

.hero-cta-column .hero-cta-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.hero-cta-column .hero-cta-button:hover::before {
  left: 100%;
}

.hero-cta-column .hero-cta-button:hover {
  transform: translateY(-2px) scale(1.01);
  box-shadow: 0 8px 24px rgba(var(--primary-color), 0.4);
  background: linear-gradient(45deg, rgba(var(--primary-color), 1.1), rgb(var(--primary-color)));
}

.hero-cta-column .hero-cta-button:active {
  transform: translateY(0) scale(1.02);
}

.hero-cta-column .cta-text {
  position: relative;
  z-index: 2;
}

.hero-cta-column .cta-icon {
  font-size: 1.2rem;
  position: relative;
  z-index: 2;
}

/* Animations */
@keyframes ctaGlow {

  0%,
  100% {
    opacity: 0.6;
  }

  50% {
    opacity: 0.8;
  }
}

@media (max-width: 1200px) {
  .hero-cta-column {
    padding: 32px 24px;
  }

  .hero-cta-column .hero-title {
    font-size: 1.9rem;
  }
}

@media (max-width: 1024px) {
  .hero-cta-column {
    padding: 28px 20px;
  }

  .hero-cta-column .hero-title {
    font-size: 1.7rem;
  }

  .hero-cta-column .hero-description {
    font-size: 0.9rem;
  }
}

@media (max-width: 768px) {
  .hero-cta-column {
    padding: 24px 20px;
    max-width: 100% !important;
  }

  .hero-cta-column .hero-title {
    font-size: 1.5rem;
  }

  .hero-cta-column .hero-description {
    font-size: 0.85rem;
    line-height: 1.5;
  }

  .hero-cta-column .hero-cta-button {
    padding: 14px 28px;
    font-size: 1rem;
  }

  .hero-cta-column .user-content {
    padding: 0px;
  }

  .hero-cta-column .game-name {
    padding: 0;
    min-height: 0;
  }

  .hero-cta-column .play-overlay {
    opacity: 1;
  }
}

@media (max-width: 480px) {

  .hero-cta-column {
    padding: 20px 16px;
    border-radius: 16px;
  }

  .hero-cta-column .hero-title {
    font-size: 1.3rem;
  }

  .hero-cta-column .hero-description {
    font-size: 0.8rem;
  }

  .hero-cta-column .hero-cta-button {
    padding: 12px 24px;
    font-size: 0.9rem;
    border-radius: 12px;
  }
}

/* User Profile Styles */
.hero-cta-column .user-profile-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  margin-bottom: 8px;
}

.hero-cta-column .profile-initials-box {
  width: 44px;
  height: 44px;
  border-radius: 16px;
  background: linear-gradient(135deg, #FFD700 0%, #ffe42d 100%);
  color: #140608;
  font-family: 'Montserrat', sans-serif;
  font-size: 1.2rem;
  font-weight: 800;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: rgba(251, 209, 45, 0.392) 0px 2px 24px;
  flex-shrink: 0;
  letter-spacing: -0.5px;
}

.hero-cta-column .profile-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 2px;
}

.hero-cta-column .profile-name {
  font-weight: 800;
  font-size: 1.3rem;
  font-family: 'Montserrat', sans-serif;
  background: linear-gradient(135deg, #FFD700 0%, #ffcc00 100%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  -webkit-text-fill-color: transparent;
  letter-spacing: 0.4px;
  margin: 0;
  line-height: 1.2;
}

.hero-cta-column .profile-meta {
  font-size: 0.78rem;
  color: #bdbdbd;
  font-weight: 400;
  margin: 0;
}

.hero-cta-column .profile-rank-badge {
  /* background: linear-gradient(135deg, #FFD700 0%, #ffe42d 100%); */
  color: #140608;
  border-radius: 9999px;
  padding: 6px 16px;
  font-family: 'Montserrat', sans-serif;
  font-weight: 700;
  font-size: 0.9rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.10);
  text-transform: uppercase;
  letter-spacing: 0.3px;
  flex-shrink: 0;
}

/* Balance Section Styles */
.hero-cta-column .balance-section {
  width: 100%;
  background: rgba(24, 24, 40, 0.6);
  border: 1px solid rgba(255, 215, 0, 0.15);
  border-radius: 16px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.10);
  padding: 8px 16px;
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.hero-cta-column .balance-header-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.hero-cta-column .balance-title {
  font-size: 0.85rem;
  font-weight: 600;
  color: #bdbdbd;
  letter-spacing: 0.04em;
  text-transform: uppercase;
  margin: 0;
}

.balance-badge-wrapper {
  background: rgba(255, 215, 0, 0.05);
  border-radius: 12px;
  display: inline-block;
  padding: 4px 12px;
  box-shadow: none;
  font-size: 0.8rem;
}

.balance-badge {
  color: rgba(255, 215, 0, 0.8);
  font-weight: 600;
  font-size: 0.75rem;
  font-family: 'Montserrat', sans-serif;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.balance-value-row {
  text-align: left;
  margin: 0;
}

.balance-value {
  font-size: 2.1rem;
  line-height: 1.3;
  font-weight: 800;
  color: #ffc600;
  letter-spacing: 0.01em;
  font-family: 'Montserrat', sans-serif;
  margin: 0;
}

.hero-cta-column .balance-meta-column {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 2px;
  font-size: 0.8rem;
  color: #bdbdbd;
  font-weight: 400;
}

.hero-cta-column .balance-points {
  color: #bdbdbd;
  margin: 0;
}

.hero-cta-column .balance-member-since {
  /* color: #bdbdbd; */
  color: #d3c959;
  margin: 0;
}

/* Last Played Section Styles */
.hero-cta-column .last-played-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 12px;
  background: rgba(24, 24, 40, 0.6);
  border: 1px solid rgba(255, 215, 0, 0.15);
  border-radius: 16px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.10);
  padding: 16px;
  position: relative;
}

.hero-cta-column .last-played-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin: 0;
}

.hero-cta-column .last-played-title {
  font-size: 1rem;
  font-weight: 600;
  color: #bdbdbd;
  letter-spacing: 0.04em;
  text-transform: uppercase;
  margin: 0;
  flex-grow: 1;
}

.hero-cta-column .last-played-content {
  width: 100%;
}

.hero-cta-column .last-played-game {
  width: 100%;
  height: auto;
  display: flex;
  align-items: center;
  gap: 12px;
  /* padding: 12px; */
  background: rgba(255, 255, 255, 0.02);
  border-radius: 12px;
  outline: 1px solid rgba(255, 255, 255, 0.05);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.hero-cta-column .last-played-game:hover {
  transform: translateY(-1px);
}

.hero-cta-column .game-image-container {
  position: relative;
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s ease;
  border-radius: 8px;
}

.last-played-game:hover .play-overlay {
  opacity: 1;
}

.hero-cta-column .play-button {
  background: rgba(255, 215, 0, 0.9);
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.hero-cta-column .play-icon {
  color: #1A1A1A;
  font-size: 14px;
  margin-left: 2px;
}

.hero-cta-column .last-played-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
  display: block;
}

.hero-cta-column .game-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 0;
}

.hero-cta-column .game-name {
  font-size: 1rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.hero-cta-column .game-provider {
  font-size: 0.8rem;
  color: #bdbdbd;
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Loader Indicator */
.hero-cta-column .loader-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 60px;
}

.hero-cta-column .spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(255, 215, 0, 0.3);
  border-top: 3px solid #FFD700;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: -64px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* Random Slot Button */
.hero-cta-column #random-slot-btn.hero-cta-button {
  width: auto;
  font-size: 0.75rem;
  font-weight: 700;
  letter-spacing: 0.3px;
  border-radius: 12px;
  padding: 4px 16px;
  background: linear-gradient(135deg, #FFD700 0%, #ffe42d 100%);
  color: #140608;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.10);
  transition: all 0.2s ease;
  text-transform: uppercase;
  display: flex;
  flex-shrink: 0;
}

#random-slot-btn.hero-cta-button:hover {
  background: linear-gradient(135deg, #ffe42d 0%, #FFD700 100%);
  color: #140608;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Roulette Container Styles */
.hero-cta-column .roulette-container {
  position: relative;
  width: 100%;
  height: 60px;
  overflow: hidden;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.05);
  display: flex;
  align-items: center;
  /* justify-content: center; */
}

.hero-cta-column .roulette-strip {
  display: flex;
  align-items: flex-start;
  height: 100%;
  padding: 4px 0;
  gap: 8px;
}

.hero-cta-column .roulette-card {
  width: 56px;
  height: 50px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
}

.hero-cta-column .roulette-card img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 6px;
}

.roulette-indicator {
  position: absolute;
  left: 50%;
  top: 0;
  width: 3px;
  height: 100%;
  background: #fff;
  transform: translateX(-50%);
  z-index: 2;
  pointer-events: none;
}

/* User Actions */
.user-actions {
  display: flex;
  gap: 8px;
  width: 100%;
}

.user-actions .hero-cta-button {
  flex: 1;
  padding: 12px 16px;
  font-size: 0.9rem;
}

.hero-cta-button.secondary {
  background:
    linear-gradient(135deg, #FFD700 0%, #ffe42d 100%),
    linear-gradient(45deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
  color: #1A1A1A;
  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.35),
    0 2px 8px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  border: 2px solid rgba(255, 215, 0, 0.3);
  max-height: 35px;
}

.hero-cta-button.secondary:hover {
  background:
    linear-gradient(135deg, #ffe42d 0%, #FFD700 100%),
    linear-gradient(45deg, rgba(255, 255, 255, 0.25) 0%, transparent 50%);
  box-shadow:
    0 10px 30px rgba(255, 215, 0, 0.45),
    0 4px 12px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  border-color: rgba(255, 215, 0, 0.5);
}
