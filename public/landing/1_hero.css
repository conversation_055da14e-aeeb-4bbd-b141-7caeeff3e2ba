/* Hero Section Styles */
.hero-section {
  padding: 20px 0 0 0;
  position: relative;
  z-index: 1;
}

.hero-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
  position: relative;
  z-index: 1;
}

.hero-content {
  display: grid;
  grid-template-columns: 9fr 4fr;
  gap: 20px;
  align-items: stretch;
}

/* Slider Column (3/4) */
.hero-slider-column {
  position: relative;
}

.hero-slider {
  background: rgba(var(--secondary-color), 0.3);
  border-radius: 20px;
  overflow: hidden;
  position: relative;
  height: 400px;
  border: 1px solid rgba(var(--primary-color), 0.1);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.2),
    0 2px 8px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.slider-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.slider-track {
  display: flex;
  width: 100%;
  height: 100%;
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: grab;
  will-change: transform;
}

.slider-track.dragging {
  transition: none;
  cursor: grabbing;
}

.slide {
  flex: 0 0 100%;
  width: 100%;
  height: 100%;
  cursor: pointer;
  position: relative;
}

.slide-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  border-radius: 20px;
}

/* Mobile optimization for hero slider images */
@media (max-width: 768px) {
  .slide-image {
    object-position: left center;
  }
}

/* Slider Dots */
.slider-dots {
  position: absolute;
  bottom: 8px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: row;
  gap: 12px;
  z-index: 10;
  background: rgba(0, 0, 0, 0.3);
  padding: 8px 16px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

.slider-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.slider-dot.active {
  background: rgb(var(--primary-color));
  border-color: rgb(var(--primary-color));
  transform: scale(1.3);
  box-shadow:
    0 0 16px rgba(var(--primary-color), 0.6),
    0 2px 8px rgba(0, 0, 0, 0.4);
}

.slider-dot:hover {
  background: rgba(var(--primary-color), 0.8);
  border-color: rgba(var(--primary-color), 0.9);
  transform: scale(1.1);
  box-shadow:
    0 0 12px rgba(var(--primary-color), 0.4),
    0 2px 8px rgba(0, 0, 0, 0.3);
}

/* Loading State */
.hero-slider.loading {
  background: rgba(var(--secondary-color), 0.2);
}

.hero-slider.loading::after {
  content: 'Slaytlar yükleniyor...';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.1rem;
  z-index: 5;
}

/* Error State */
.hero-slider.error::after {
  content: 'Slaytlar yüklenemedi';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: rgba(255, 100, 100, 0.8);
  font-size: 1.1rem;
  z-index: 5;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .hero-container {
    padding: 0 20px;
  }

  .hero-content {
    gap: 24px;
  }

  .hero-slider {
    height: 350px;
  }

  .slider-dots {
    bottom: 16px;
    padding: 6px 14px;
  }
}

@media (max-width: 1024px) {
  .hero-content {
    grid-template-columns: 2fr 1fr;
    gap: 20px;
  }

  .hero-slider {
    height: 320px;
  }

  .slider-dots {
    bottom: 14px;
    gap: 10px;
    padding: 6px 12px;
  }
}

@media (max-width: 768px) {
  .hero-section {
    padding: 15px 0;
  }

  .hero-container {
    padding: 0 16px;
  }

  .hero-content {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .hero-slider {
    height: 280px;
  }

  .slider-dots {
    bottom: 12px;
    gap: 8px;
    padding: 5px 10px;
  }

  .slider-dot {
    width: 10px;
    height: 10px;
  }
}

@media (max-width: 480px) {
  .hero-section {
    padding: 10px 0;
  }

  .hero-container {
    padding: 0 12px;
  }

  .hero-content {
    gap: 16px;
  }

  .hero-slider {
    height: 240px;
    border-radius: 16px;
  }

  .slider-dots {
    bottom: 12px;
    gap: 8px;
    padding: 6px 12px;
  }

  .slider-dot {
    width: 10px;
    height: 10px;
  }
}
