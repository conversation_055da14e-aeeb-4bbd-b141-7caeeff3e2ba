/* Providers Marquee Section */

.providers-marquee {
  margin: 40px 0;
  position: relative;
  padding: 0 0 20px 0;
  overflow: hidden;
}

/* Mobile margin optimization */
@media (max-width: 768px) {
  .providers-marquee {
    display: none;

    margin: 20px 0;
    padding: 0 0 15px 0;
    /* Hide by default on mobile until scroll reaches section */
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
  }

  .providers-marquee.visible {
    opacity: 1;
    transform: translateY(0);
  }

  .providers-marquee-header {
    margin-bottom: 20px;
    padding-bottom: 12px;
  }
}

@media (max-width: 480px) {
  .providers-marquee {
    margin: 15px 0;
    padding: 0 0 10px 0;
  }

  .providers-marquee-header {
    margin-bottom: 16px;
    padding-bottom: 8px;
  }
}

.providers-marquee-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
  position: relative;
  z-index: 1;
}

.providers-marquee-header {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 32px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(var(--primary-color), 0.1);
}

.providers-marquee-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow: hidden;
}

.providers-row {
  display: flex;
  gap: 20px;
  white-space: nowrap;
  width: fit-content;
}

.providers-marquee.visible .providers-row:nth-child(1) {
  animation: marqueeLeft 120s linear infinite;
  margin-left: 0;
}

.providers-marquee.visible .providers-row:nth-child(2) {
  animation: marqueeRight 130s linear infinite;
  margin-left: -120px;
}

.providers-marquee.visible .providers-row:nth-child(3) {
  animation: marqueeLeft 140s linear infinite;
  margin-left: -160px;
  animation-duration: 120s;
}

.provider-card {
  flex-shrink: 0;
  width: 240px;
  height: 80px;
  border-radius: 20px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  position: relative;
  background: rgb(var(--secondary-color));
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.15),
    0 2px 4px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding: 16px 20px;
  gap: 16px;
}

.provider-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    linear-gradient(90deg,
      rgba(var(--primary-color), 0.1) 0%,
      transparent 20%,
      transparent 80%,
      rgba(var(--primary-color), 0.1) 100%);
  z-index: 1;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  border-radius: 20px;
  animation: shimmerFlow 3s ease-in-out infinite;
}

.provider-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background:
    linear-gradient(90deg,
      transparent 0%,
      rgba(var(--primary-color), 0.15) 50%,
      transparent 100%);
  z-index: 2;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 20px;
}

.provider-card:hover::before {
  opacity: 1;
}

.provider-card:hover::after {
  left: 100%;
}

.provider-card:hover {
  transform: translateY(-6px) scale(1.03);
  background: rgb(var(--secondary-color));
  box-shadow:
    0 8px 24px rgba(var(--primary-color), 0.2),
    0 4px 12px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.provider-image {
  width: 48px;
  height: 48px;
  object-fit: contain;
  display: block;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 3;
  margin: 0;
  filter: brightness(0.95) contrast(1.1) saturate(1.1);
  border-radius: 8px;
  flex-shrink: 0;
}

.provider-card:hover .provider-image {
  transform: scale(1.1);
  filter: brightness(1.1) contrast(1.2) saturate(1.2);
}

.provider-name {
  font-size: 0.9rem;
  font-weight: 600;
  color: rgb(var(--primary-color));
  text-align: left;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  position: relative;
  z-index: 3;
  line-height: 1.3;
  letter-spacing: 0.25px;
  text-transform: uppercase;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.provider-card:hover .provider-name {
  color: rgb(var(--primary-color));
  text-shadow:
    0 2px 8px rgba(var(--primary-color), 0.4),
    0 1px 2px rgba(0, 0, 0, 0.6);
  transform: scale(1.05);
}

/* Provider Loading Placeholders */
.provider-card.loading-placeholder {
  animation: pulse 1.5s ease-in-out infinite;
}

.provider-image-placeholder {
  width: 44px;
  height: 44px;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.1) 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 10px;
  flex-shrink: 0;
}

.provider-name-placeholder {
  height: 18px;
  flex: 1;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.1) 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 6px;
}

/* Responsive Design for Provider Cards */
@media (max-width: 1024px) {
  .provider-card {
    width: 220px;
    height: 72px;
    padding: 14px 18px;
    border-radius: 14px;
    gap: 14px;
  }

  .provider-image {
    width: 44px;
    height: 44px;
    border-radius: 8px;
  }

  .provider-name {
    font-size: 0.85rem;
  }

  .provider-image-placeholder {
    width: 44px;
    height: 44px;
  }
}

@media (max-width: 768px) {
  .providers-row:nth-child(1) {
    /* animation-duration: 80s; */
    animation-duration: 0s;
  }

  .providers-row:nth-child(2) {
    /* animation-duration: 85s; */
    animation-duration: 0s;
  }

  .providers-row:nth-child(3) {
    /* animation-duration: 90s; */
    animation-duration: 0s;
  }

  .provider-card {
    width: 200px;
    height: 68px;
    padding: 12px 16px;
    border-radius: 12px;
    gap: 12px;
  }

  .provider-image {
    width: 40px;
    height: 40px;
    border-radius: 6px;
  }

  .provider-name {
    font-size: 0.8rem;
  }

  .provider-image-placeholder {
    width: 40px;
    height: 40px;
  }
}

@media (max-width: 480px) {
  .providers-row:nth-child(1) {
    /* animation-duration: 70s; */
    animation-duration: 0s;
  }

  .providers-row:nth-child(2) {
    /* animation-duration: 75s; */
    animation-duration: 0s;
  }

  .providers-row:nth-child(3) {
    /* animation-duration: 80s; */
    animation-duration: 0s;
  }

  .provider-card {
    width: 180px;
    height: 64px;
    padding: 10px 14px;
    border-radius: 10px;
    gap: 10px;
  }

  .provider-image {
    width: 36px;
    height: 36px;
    border-radius: 6px;
  }

  .provider-name {
    font-size: 0.75rem;
  }

  .provider-image-placeholder {
    width: 36px;
    height: 36px;
  }
}

/* Extra small mobile devices */
@media (max-width: 360px) {
  .providers-row:nth-child(1) {
    animation-duration: 60s;
  }

  .providers-row:nth-child(2) {
    animation-duration: 65s;
  }

  .providers-row:nth-child(3) {
    animation-duration: 70s;
  }

  .provider-card {
    width: 160px;
    height: 60px;
    padding: 8px 12px;
    border-radius: 8px;
    gap: 8px;
  }

  .provider-image {
    width: 32px;
    height: 32px;
    border-radius: 4px;
  }

  .provider-name {
    font-size: 0.7rem;
  }

  .provider-image-placeholder {
    width: 28px;
    height: 28px;
    margin-bottom: 4px;
  }
}

/* Seamless Marquee Animations */
@keyframes marqueeLeft {
  0% {
    transform: translateX(0);
  }

  100% {
    transform: translateX(-50%);
  }
}

@keyframes marqueeRight {
  0% {
    transform: translateX(-50%);
  }

  100% {
    transform: translateX(0);
  }
}

/* Provider Card Animations */
@keyframes shimmerFlow {

  0%,
  100% {
    transform: translateX(0) scaleX(1);
    opacity: 0.3;
  }

  50% {
    transform: translateX(10px) scaleX(1.1);
    opacity: 0.6;
  }
}
