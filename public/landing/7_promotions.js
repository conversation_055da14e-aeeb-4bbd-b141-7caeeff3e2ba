// Promotions Section JavaScript
window.$LANDING.onLifecycle({
  selector: `.makrobet-landing`,
  page: /^\/\w+$/,
  onMount: (_, __, kill) => {
    // Configuration
    const PROMOTION_SLIDER_TYPE = 35
    const SLIDER_DEVICE_TYPE = window.origin.includes('//m.') ? 2 : 1
    const USE_MOCKED_DATA = false // Set to false to use API
    const SLIDER_API_URL = `https://pn17.pfnow100.com/api/tr/consumer`
    const IMAGE_BASE_URL = window.origin

    // Mocked promotions data (fallback)
    const MOCKED_PROMOTIONS = [
      {
        id: 1,
        path: `/cdn/makrobet/upload_files/calismayuzeyi1.png`,
        url: `/contents/promotions`,
        content: `An<PERSON><PERSON>k <PERSON> %20'ye Varan Bonus`,
        type: 35
      },
      {
        id: 2,
        path: `/cdn/makrobet/upload_files/calismayuzeyi2.png`,
        url: `/contents/promotions`,
        content: `%25 SLOT YATIRIM BONUSU`,
        type: 35
      },
      {
        id: 3,
        path: `/cdn/makrobet/upload_files/calismayuzeyi3.png`,
        url: `/contents/promotions`,
        content: `250 TL DENEME BONUSU`,
        type: 35
      },
      {
        id: 4,
        path: `/cdn/makrobet/upload_files/calismayuzeyi4.png`,
        url: `/contents/promotions`,
        content: `CASINO BOOST DAYS`,
        type: 35
      },
      {
        id: 5,
        path: `/cdn/makrobet/upload_files/calismayuzeyi5.png`,
        url: `/contents/promotions`,
        content: `ERKEN ÖDEME`,
        type: 35
      }
    ]

    // DOM elements
    const promotionsGrid = document.getElementById(`promotions-grid`)
    const promotionsSection = document.querySelector('.promotions-section')

    // Check if elements exist
    if (!promotionsGrid || !promotionsSection) {
      console.error(`Promotions: Required DOM elements not found`)
      return
    }

    // Mobile detection
    function isMobileDevice() {
      return window.innerWidth <= 768
    }

    // Slider instance
    let promotionSlider = null

    // API Functions
    async function fetchPromotions() {
      try {
        console.log(`API'den promosyonlar alınıyor...`)

        const response = await fetch(SLIDER_API_URL, {
          method: `GET`,
          headers: {
            [`Content-Type`]: `application/json`
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const data = await response.json()

        if (!data.sliders || !Array.isArray(data.sliders)) {
          throw new Error(`Invalid response format`)
        }

        return data.sliders.filter(s => s.type === PROMOTION_SLIDER_TYPE && s.m_t === SLIDER_DEVICE_TYPE)
      } catch (error) {
        console.error(`Promosyonlar alınırken hata:`, error)
        throw error
      }
    }

    // Function to create promotion card for slider
    function createPromotionCardForSlider(promotion) {
      const fallbackImageUrl = `data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE2MCIgdmlld0JveD0iMCAwIDIwMCAxNjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTYwIiBmaWxsPSIjMmIyYjRmIi8+Cjx0ZXh0IHg9IjEwMCIgeT0iODAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGZpbGw9IiNmYmQxMmQiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCI+UHJvbW90aW9uPC90ZXh0Pgo8L3N2Zz4=`

      // Create main card container
      const promotionCard = document.createElement(`div`)
      promotionCard.className = `promotion-card`
      promotionCard.setAttribute(`data-promotion-id`, promotion.id)
      promotionCard.setAttribute(`data-game-id`, promotion.id) // For slider compatibility
      promotionCard.setAttribute(`tabindex`, `0`)
      promotionCard.setAttribute(`role`, `button`)

      // Create image element
      const promotionImage = document.createElement(`img`)

      // Handle both mocked data (full path) and API data (path only)
      if (USE_MOCKED_DATA || promotion.path.startsWith(`/`)) {
        promotionImage.src = promotion.path
      } else {
        promotionImage.src = promotion.path.startsWith('http') ? promotion.path : `${IMAGE_BASE_URL}${promotion.path}`
      }

      promotionImage.alt = promotion.content || `Promosyon ${promotion.id}`
      promotionImage.className = `promotion-image`
      promotionImage.addEventListener(`error`, function () {
        this.src = fallbackImageUrl
      })

      // Assemble the card
      promotionCard.appendChild(promotionImage)

      return promotionCard
    }

    // Function to create promotion card for grid
    function createPromotionCardForGrid(promotion) {
      const fallbackImageUrl = `data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE2MCIgdmlld0JveD0iMCAwIDIwMCAxNjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTYwIiBmaWxsPSIjMmIyYjRmIi8+Cjx0ZXh0IHg9IjEwMCIgeT0iODAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGZpbGw9IiNmYmQxMmQiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCI+UHJvbW90aW9uPC90ZXh0Pgo8L3N2Zz4=`

      // Create main card container
      const promotionCard = document.createElement(`div`)
      promotionCard.className = `promotion-card`
      promotionCard.setAttribute(`data-promotion-id`, promotion.id)
      promotionCard.setAttribute(`tabindex`, `0`)
      promotionCard.setAttribute(`role`, `button`)

      // Create image element
      const promotionImage = document.createElement(`img`)

      // Handle both mocked data (full path) and API data (path only)
      if (USE_MOCKED_DATA || promotion.path.startsWith(`/`)) {
        promotionImage.src = promotion.path
      } else {
        promotionImage.src = promotion.path.startsWith('http') ? promotion.path : `${IMAGE_BASE_URL}${promotion.path}`
      }

      promotionImage.alt = promotion.content || `Promosyon ${promotion.id}`
      promotionImage.className = `promotion-image`
      promotionImage.addEventListener(`error`, function () {
        this.src = fallbackImageUrl
      })

      // Assemble the card
      promotionCard.appendChild(promotionImage)

      // Add click handler with scroll detection
      let isScrolling = false
      let touchStartY = 0

      const handlePromotionClick = () => {
        console.log(`Promosyona tıklandı:`, promotion.content)
        if (promotion.url) {
          if (promotion.url.startsWith(`/`)) {
            $LANDING.navigate(promotion.url)
          } else {
            window.open(promotion.url, `_blank`)
          }
        } else {
          $LANDING.navigate(`/contents/promotions`)
        }
      }

      promotionCard.addEventListener(`click`, handlePromotionClick)

      // Add touch support with scroll detection
      promotionCard.addEventListener(`touchstart`, e => {
        isScrolling = false
        touchStartY = e.touches[0].clientY
      })

      promotionCard.addEventListener(`touchmove`, e => {
        const touchMoveY = e.touches[0].clientY
        const deltaY = Math.abs(touchMoveY - touchStartY)

        if (deltaY > 10) {
          isScrolling = true
        }
      })

      promotionCard.addEventListener(`touchend`, e => {
        if (isScrolling) {
          return
        }

        e.preventDefault()
        handlePromotionClick()
      })

      // Add keyboard support
      promotionCard.addEventListener(`keydown`, e => {
        if (e.key === `Enter` || e.key === ` `) {
          e.preventDefault()
          promotionCard.click()
        }
      })

      return promotionCard
    }

    // Function to load promotions data
    async function loadPromotions() {
      try {
        console.log(`Promosyonlar yükleniyor...`)

        let promotionsData

        if (USE_MOCKED_DATA) {
          // Use mocked data
          console.log(`Sahte promosyon verileri kullanılıyor`)
          promotionsData = MOCKED_PROMOTIONS
        } else {
          // Fetch from API
          promotionsData = await fetchPromotions()
        }

        if (!promotionsData.length) {
          console.warn(`Promosyon bulunamadı`)
          showEmptyState()
          return
        }

        // Store data globally for resize handling
        window.promotionsData = promotionsData

        displayPromotions(promotionsData)
        console.log(`${promotionsData.length} promosyon yüklendi`)
      } catch (error) {
        console.error(`Promosyonlar yüklenirken hata:`, error)
        showErrorState()
      }
    }

    // Function to display promotions
    function displayPromotions(promotions) {
      if (promotions.length === 0) {
        showEmptyState()
        return
      }

      if (isMobileDevice()) {
        displayPromotionsAsSlider(promotions)
      } else {
        displayPromotionsAsGrid(promotions)
      }

      console.log(`Loaded ${promotions.length} promotions`)
    }

    // Function to display promotions as grid (desktop/tablet)
    function displayPromotionsAsGrid(promotions) {
      // Clear loading placeholders
      promotionsGrid.innerHTML = ``

      // Create promotion cards
      promotions.forEach(promotion => {
        const card = createPromotionCardForGrid(promotion)
        promotionsGrid.appendChild(card)
      })
    }

    // Function to display promotions as slider (mobile)
    function displayPromotionsAsSlider(promotions) {
      // Clear existing content and create slider structure
      const promotionsContent = promotionsGrid.parentElement
      promotionsContent.innerHTML = `
      <div class="game-section-header">
        <h2 class="game-section-title">
          <img src="https://cdn.makroz.org/makrobet/icons/header/promotions.png" alt="Promosyonlar" class="game-section-icon">
          Promosyonlar
        </h2>
        <div class="slider-controls">
          <button class="slider-arrow" id="promotions-prev-arrow" aria-label="Önceki promosyonlar">‹</button>
          <button class="slider-arrow" id="promotions-next-arrow" aria-label="Sonraki promosyonlar">›</button>
        </div>
      </div>
      <div class="promotions-slider">
        <div class="promotions-track" id="promotions-track">
          <!-- Promotions will be loaded here dynamically -->
        </div>
      </div>
    `

      // Create custom card function that includes navigation
      const createPromotionCardWithNavigation = promotion => {
        const card = createPromotionCardForSlider(promotion)

        // Add click handler for navigation
        const handlePromotionClick = () => {
          console.log(`Promosyona tıklandı:`, promotion.content)
          if (promotion.url) {
            if (promotion.url.startsWith(`/`)) {
              $LANDING.navigate(promotion.url)
            } else {
              window.open(promotion.url, `_blank`)
            }
          } else {
            $LANDING.navigate(`/contents/promotions`)
          }
        }

        card.addEventListener(`click`, handlePromotionClick)

        return card
      }

      // Initialize slider
      promotionSlider = window.GameSlider({
        sectionId: 'promotions-section',
        trackId: 'promotions-track',
        prevArrowId: 'promotions-prev-arrow',
        nextArrowId: 'promotions-next-arrow',
        games: promotions,
        createCardFunction: createPromotionCardWithNavigation,
        navigationPath: '/contents/promotions',
        noItemsMessage: 'Şu anda promosyon bulunmuyor.'
      })
    }

    // Function to show empty state
    function showEmptyState() {
      const promotionsContent = promotionsGrid.parentElement
      promotionsContent.innerHTML = ``

      const emptyContainer = document.createElement(`div`)
      emptyContainer.style.textAlign = `center`
      emptyContainer.style.padding = `40px`
      emptyContainer.style.color = `rgba(255, 255, 255, 0.6)`

      if (!isMobileDevice()) {
        emptyContainer.style.gridColumn = `1 / -1`
      }

      const emptyMessage = document.createElement(`p`)
      emptyMessage.textContent = `Şu anda promosyon bulunmuyor.`

      emptyContainer.appendChild(emptyMessage)
      promotionsContent.appendChild(emptyContainer)
    }

    // Function to show error state
    function showErrorState() {
      const promotionsContent = promotionsGrid.parentElement
      promotionsContent.innerHTML = ``

      const errorContainer = document.createElement(`div`)
      errorContainer.style.textAlign = `center`
      errorContainer.style.padding = `40px`
      errorContainer.style.color = `rgba(255, 255, 255, 0.6)`

      if (!isMobileDevice()) {
        errorContainer.style.gridColumn = `1 / -1`
      }

      const errorMessage = document.createElement(`p`)
      errorMessage.textContent = `Promosyonlar yüklenemedi. Lütfen daha sonra tekrar deneyin.`

      errorContainer.appendChild(errorMessage)
      promotionsContent.appendChild(errorContainer)
    }

    // Function to handle window resize
    function handleResize() {
      // Debounce resize events
      clearTimeout(window.promotionsResizeTimeout)
      window.promotionsResizeTimeout = setTimeout(() => {
        // Re-render promotions if data is available
        if (window.promotionsData && window.promotionsData.length > 0) {
          displayPromotions(window.promotionsData)
        }
      }, 250)
    }

    // Initialize
    loadPromotions()

    // Add resize event listener
    window.addEventListener('resize', handleResize)

    // Make function globally available for debugging
    window.loadPromotionsSection = loadPromotions

    window.addEventListener(
      '@makrobet/unload/landing',
      () => {
        // Cleanup
        window.removeEventListener('resize', handleResize)
        clearTimeout(window.promotionsResizeTimeout)
        if (window.promotionsData) {
          delete window.promotionsData
        }
        kill()
      },
      { once: true }
    )
  }
})
