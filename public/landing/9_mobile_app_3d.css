/* 3D Mobile App Section Styles */

/* Phone Container - 3D Scene */
.phone-container {
  overflow: visible;
  position: relative;
  width: 500px;
  height: 750px;
  overflow: visible;
  display: flex;
  align-items: center;
  justify-content: center;
  perspective: 1000px;
}

.phone-container canvas {
  max-width: 100%;
  max-height: 100%;
  border-radius: 15px;
  transition: all 0.3s ease;
}

/* .phone-container:hover canvas {
  transform: scale(1.05);
} */

/* Platform Highlight */
.platform-highlight {
  animation: platformPulse 2s ease-in-out infinite;
  border: 2px solid #fbd12d !important;
  box-shadow: 0 0 20px rgba(251, 209, 45, 0.4) !important;
}

@keyframes platformPulse {

  0%,
  100% {
    box-shadow: 0 0 20px rgba(251, 209, 45, 0.4);
  }

  50% {
    box-shadow: 0 0 30px rgba(251, 209, 45, 0.6);
  }
}

/* Loading State */
/* .phone-container.loading {
  background: linear-gradient(45deg, rgba(251, 209, 45, 0.1), rgba(43, 43, 79, 0.1));
  background-size: 400% 400%;
  animation: loadingGradient 2s ease-in-out infinite;
}

@keyframes loadingGradient {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
} */

/* 3D Scene Enhancements */
.phone-container::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 125%;
  height: 120%;
  background: radial-gradient(circle, rgba(251, 209, 45, 0.05) 0%, transparent 60%);
  transform: translate(-50%, -50%);
  pointer-events: none;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.phone-container:hover::before {
  opacity: 1;
}

/* Performance Optimizations */
.phone-container {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Error Fallback Styles */
.phone-container.error {
  background: rgba(255, 0, 0, 0.1);
  border: 2px dashed rgba(255, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.7);
  font-size: 1rem;
  text-align: center;
}

.phone-container.error::after {
  content: '3D Model Failed to Load\AUsing Fallback Display';
  white-space: pre;
  line-height: 1.5;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .phone-container {
    width: 300px;
    height: 450px;
  }

  .phone-container canvas {
    border-radius: 10px;
  }

  /* .phone-container:hover canvas {
    transform: scale(1.02);
  } */
}

@media (max-width: 480px) {
  .phone-container {
    width: 250px;
    height: 375px;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {

  .phone-container,
  .phone-container canvas,
  .phone-container::before {
    animation: none !important;
    transition: none !important;
  }

  .phone-container:hover canvas {
    transform: none;
  }

  .platform-highlight {
    animation: none;
  }
}

/* High Performance Mode */
@media (min-width: 1200px) and (min-height: 800px) {
  .phone-container {
    width: 600px;
    height: 900px;
  }
}

/* WebGL Support Detection */
.no-webgl .phone-container {
  background: linear-gradient(135deg, #2b2b4f, #1a1a2e);
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
  text-align: center;
  padding: 20px;
}

.no-webgl .phone-container::after {
  content: '3D Not Supported\AYour browser doesn\'t support WebGL\AShowing fallback display';
  white-space: pre;
  line-height: 1.6;
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
  /* .phone-container:hover canvas {
    transform: scale(1.02);
  } */

  .phone-container::before {
    opacity: 0.5;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .phone-container::before {
    background: radial-gradient(circle, rgba(251, 209, 45, 0.08) 0%, transparent 60%);
  }
}

/* Loading Spinner for 3D Scene */
.phone-container.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 40px;
  height: 40px;
  border: 3px solid rgba(251, 209, 45, 0.3);
  border-top: 3px solid #fbd12d;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* 3D Scene Container Enhancements */
.mobile-app-image {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

/* Glow Effect for 3D Phone */
.phone-container canvas {
  filter: drop-shadow(0 10px 30px rgba(251, 209, 45, 0.2));
  transition: filter 0.3s ease;
}

.phone-container:hover canvas {
  filter: drop-shadow(0 15px 40px rgba(251, 209, 45, 0.3));
}

/* Performance Indicators */
.phone-container[data-performance="low"] canvas {
  image-rendering: pixelated;
  image-rendering: -moz-crisp-edges;
  image-rendering: crisp-edges;
}

.phone-container[data-performance="high"] canvas {
  image-rendering: auto;
  image-rendering: smooth;
}

/* Interaction Feedback */
.phone-container.interactive {
  cursor: pointer;
}

/* .phone-container.interactive:active canvas {
  transform: scale(0.98);
} */

/* Fallback for Old Browsers */
.no-js .phone-container,
.no-canvas .phone-container {
  background: url('https://cdn.makroz.org/makrobet/iphone-mockup.png') center/contain no-repeat;
  background-color: transparent;
}

.no-js .phone-container::after,
.no-canvas .phone-container::after {
  content: none;
}
