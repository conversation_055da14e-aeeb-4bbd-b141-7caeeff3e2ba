/* Mobile App Download Section */
.mobile-app-section {
  position: relative;
  padding: 80px 0;
  background: linear-gradient(135deg, #2b2b4f 0%, #1a1a3a 50%, #2b2b4f 100%);
  overflow: hidden;
  margin-top: 60px;
}

/* Background Visual Effects */
.mobile-app-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.bg-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(90deg, rgba(251, 209, 45, 0.1) 1px, transparent 1px),
    linear-gradient(rgba(251, 209, 45, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: moveLines 20s linear infinite;
}

.bg-curves {
  position: absolute;
  top: -50%;
  left: -10%;
  width: 120%;
  height: 200%;
  background: radial-gradient(ellipse at center, rgba(251, 209, 45, 0.05) 0%, transparent 70%);
  animation: rotateCurves 30s linear infinite;
}

.bg-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(2px 2px at 20px 30px, rgba(251, 209, 45, 0.3), transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(251, 209, 45, 0.2), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(251, 209, 45, 0.4), transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(251, 209, 45, 0.2), transparent);
  background-repeat: repeat;
  background-size: 150px 100px;
  animation: floatParticles 25s ease-in-out infinite;
}

/* Container and Layout */
.mobile-app-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 2;
}

.mobile-app-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
  min-height: 500px;
}

/* Text Content */
.mobile-app-text {
  color: white;
}

.mobile-app-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 20px;
  line-height: 1.2;
}

.title-highlight {
  color: #fbd12d;
  text-shadow: 0 0 20px rgba(251, 209, 45, 0.5);
}

.mobile-app-subtitle {
  font-size: 1.2rem;
  line-height: 1.6;
  margin-bottom: 40px;
  opacity: 0.9;
}



/* App Statistics */
.app-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin: 30px 0;
  padding: 25px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  border: 1px solid rgba(251, 209, 45, 0.1);
}

.stat-item {
  text-align: center;
  padding: 15px 10px;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: #fbd12d;
  display: block;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.8;
  font-weight: 500;
}

/* App Benefits */
.app-benefits {
  margin: 30px 0;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  margin-top: 20px;
}

.benefit-item {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 10px;
  border-left: 3px solid #fbd12d;
  transition: all 0.3s ease;
}

.benefit-item:hover {
  background: rgba(255, 255, 255, 0.06);
  transform: translateY(-2px);
}

.benefit-icon {
  font-size: 1.2rem;
  margin-top: 2px;
  color: #fbd12d;
}

.benefit-text {
  font-size: 0.95rem;
  line-height: 1.4;
  font-weight: 500;
}



/* Download Buttons */
.mobile-app-buttons {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.app-download-btn {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px 25px;
  background: linear-gradient(135deg, #fbd12d 0%, #f4c430 100%);
  color: #2b2b4f;
  text-decoration: none;
  border-radius: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(251, 209, 45, 0.3);
  position: relative;
  overflow: hidden;
}

.app-download-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.app-download-btn:hover::before {
  left: 100%;
}

.app-download-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 25px rgba(251, 209, 45, 0.4);
}

.btn-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-text {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.btn-label {
  font-size: 1rem;
  font-weight: 600;
}

.btn-platform {
  font-size: 0.9rem;
  opacity: 0.8;
  font-weight: 500;
}

/* Phone Image */
.mobile-app-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Device-specific visibility */
.mobile-app-image.mobile {
  display: none;
  /* Hidden by default, shown on mobile */
}

.mobile-app-image.desktop {
  display: flex;
  /* Shown by default on desktop */
}

.phone-container {
  position: relative;
  animation: floatPhone 6s ease-in-out infinite;
  width: 500px;
  height: 750px;
  /* overflow: hidden; */
}

/* Money Rain Animation - Enhanced & Optimized */
.money-rain {
  position: absolute;
  top: -100px;
  left: -50%;
  width: 200%;
  height: calc(100% + 200px);
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.5s ease;
  z-index: 10;
  contain: layout style paint;
  transform: translateZ(0);
  overflow: hidden;
}

.phone-container:hover .money-rain {
  opacity: 1;
}

/* Performance optimizations */
@media (prefers-reduced-motion: reduce) {
  .money-rain {
    display: none !important;
  }
}

/* Disable on very small screens for performance */
@media (max-width: 480px) {
  .money-rain {
    display: none;
  }
}

.money-bill {
  position: absolute;
  width: 35px;
  height: 22px;
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 50%, #DAA520 100%);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
  color: #1a1a1a;
  box-shadow:
    0 3px 10px rgba(255, 215, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(218, 165, 32, 0.8);
  will-change: transform, opacity;
  backface-visibility: hidden;
  transform: translateZ(0);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.money-bill::before {
  content: '💰';
  font-family: 'Apple Color Emoji', 'Segoe UI Emoji', 'Noto Color Emoji', sans-serif;
  font-size: 16px;
}

/* Add variety with different bill types and money emojis */
.money-bill:nth-child(odd) {
  background: linear-gradient(135deg, #32CD32 0%, #228B22 50%, #006400 100%);
  color: #ffffff;
  border-color: rgba(34, 139, 34, 0.8);
  box-shadow:
    0 3px 10px rgba(50, 205, 50, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1);
}

.money-bill:nth-child(odd)::before {
  content: '💵';
}

.money-bill:nth-child(3n) {
  background: linear-gradient(135deg, #FF6347 0%, #DC143C 50%, #B22222 100%);
  color: #ffffff;
  border-color: rgba(220, 20, 60, 0.8);
  box-shadow:
    0 3px 10px rgba(255, 99, 71, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1);
}

.money-bill:nth-child(3n)::before {
  content: '💴';
}

.money-bill:nth-child(5n) {
  background: linear-gradient(135deg, #9370DB 0%, #8A2BE2 50%, #4B0082 100%);
  color: #ffffff;
  border-color: rgba(138, 43, 226, 0.8);
  box-shadow:
    0 3px 10px rgba(147, 112, 219, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1);
}

.money-bill:nth-child(5n)::before {
  content: '💶';
}

.money-bill:nth-child(7n)::before {
  content: '💷';
}

.money-bill:nth-child(11n)::before {
  content: '🤑';
}

.money-bill:nth-child(1) {
  left: 8%;
  animation: fallDownOptimized 3.2s ease-in infinite;
  animation-delay: 0.1s;
}

.money-bill:nth-child(2) {
  left: 22%;
  animation: swayFall 3.5s ease-in-out infinite;
  animation-delay: 0.6s;
}

.money-bill:nth-child(3) {
  left: 36%;
  animation: fallDownOptimized 2.8s ease-in infinite;
  animation-delay: 0.3s;
}

.money-bill:nth-child(4) {
  left: 50%;
  animation: swayFall 3.1s ease-in-out infinite;
  animation-delay: 1.1s;
}

.money-bill:nth-child(5) {
  left: 64%;
  animation: fallDownOptimized 3.4s ease-in infinite;
  animation-delay: 0.5s;
}

.money-bill:nth-child(6) {
  left: 78%;
  animation: swayFall 2.9s ease-in-out infinite;
  animation-delay: 1.4s;
}

.money-bill:nth-child(7) {
  left: 14%;
  animation: fallDownOptimized 3.0s ease-in infinite;
  animation-delay: 0.9s;
}

.money-bill:nth-child(8) {
  left: 28%;
  animation: swayFall 3.3s ease-in-out infinite;
  animation-delay: 1.6s;
}

.money-bill:nth-child(9) {
  left: 42%;
  animation: fallDownOptimized 2.7s ease-in infinite;
  animation-delay: 0.2s;
}

.money-bill:nth-child(10) {
  left: 56%;
  animation: swayFall 3.6s ease-in-out infinite;
  animation-delay: 1.8s;
}

.money-bill:nth-child(11) {
  left: 70%;
  animation: fallDownOptimized 2.5s ease-in infinite;
  animation-delay: 2.1s;
}

.money-bill:nth-child(12) {
  left: 84%;
  animation: swayFall 3.8s ease-in-out infinite;
  animation-delay: 0.4s;
}

.phone-mockup {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: drop-shadow(0 20px 40px rgba(0, 0, 0, 0.3));
  position: relative;
  z-index: 2;
  transition: filter 0.4s ease, transform 0.3s ease;
  transform-origin: center center;
  border-radius: 0;
  outline: none;
  border: none;
  background: transparent;
}

/* Show video on desktop, hide image */
.phone-video {
  display: block;
}

.phone-image {
  display: none;
}

/* Ensure video compatibility across browsers */
.phone-video {
  -webkit-playsinline: true;
  -webkit-background-size: cover;
  -moz-background-size: cover;
  -o-background-size: cover;
  background-size: cover;
}

/* Safari-specific video optimizations */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  .phone-video {
    -webkit-transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    -webkit-perspective: 1000;
  }
}

/* Hide mobile phone image on desktop */
.mobile-phone-image {
  display: none;
}

.phone-mockup::-webkit-media-controls {
  display: none !important;
}

.phone-mockup::-webkit-media-controls-panel {
  display: none !important;
}

.phone-mockup::-webkit-media-controls-play-button {
  display: none !important;
}

.phone-mockup::-webkit-media-controls-start-playback-button {
  display: none !important;
}

/* Additional video controls hiding for other browsers */
.phone-mockup::-moz-media-controls {
  display: none !important;
}

.phone-mockup::-ms-media-controls {
  display: none !important;
}

.phone-mockup::media-controls {
  display: none !important;
}

.phone-mockup {
  pointer-events: none;
  /* Prevent direct video interaction */
}

/* Glow effects removed as requested */

/* Screen glow effects removed as requested */

/* Floating particles removed as requested */

/* Hover and touch effects removed as requested */

/* Animations */
@keyframes moveLines {
  0% {
    transform: translate(0, 0);
  }

  100% {
    transform: translate(50px, 50px);
  }
}

@keyframes rotateCurves {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes floatParticles {

  0%,
  100% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-20px);
  }
}

@keyframes floatPhone {

  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }

  25% {
    transform: translateY(-8px) rotate(0.5deg);
  }

  50% {
    transform: translateY(-15px) rotate(0deg);
  }

  75% {
    transform: translateY(-8px) rotate(-0.5deg);
  }
}

@keyframes fallDownOptimized {
  0% {
    transform: translate3d(0, -100px, 0) rotate(0deg) scale(0.8);
    opacity: 0;
  }

  5% {
    opacity: 1;
    transform: translate3d(0, -80px, 0) rotate(10deg) scale(1);
  }

  25% {
    transform: translate3d(8px, 25vh, 0) rotate(90deg) scale(1.1);
  }

  50% {
    transform: translate3d(-3px, 50vh, 0) rotate(180deg) scale(1);
  }

  75% {
    transform: translate3d(6px, 75vh, 0) rotate(270deg) scale(0.9);
  }

  95% {
    opacity: 1;
    transform: translate3d(-2px, calc(100vh + 20px), 0) rotate(350deg) scale(0.8);
  }

  100% {
    transform: translate3d(0, calc(100vh + 100px), 0) rotate(360deg) scale(0.6);
    opacity: 0;
  }
}

/* Add more realistic swaying animation */
@keyframes swayFall {
  0% {
    transform: translate3d(0, -100px, 0) rotate(0deg) scale(0.8);
    opacity: 0;
  }

  5% {
    opacity: 1;
    transform: translate3d(0, -80px, 0) rotate(15deg) scale(1);
  }

  20% {
    transform: translate3d(-10px, 20vh, 0) rotate(45deg) scale(1.05);
  }

  40% {
    transform: translate3d(12px, 40vh, 0) rotate(135deg) scale(1);
  }

  60% {
    transform: translate3d(-8px, 60vh, 0) rotate(225deg) scale(0.95);
  }

  80% {
    transform: translate3d(5px, 80vh, 0) rotate(315deg) scale(0.9);
  }

  95% {
    opacity: 1;
    transform: translate3d(-3px, calc(100vh + 20px), 0) rotate(360deg) scale(0.8);
  }

  100% {
    transform: translate3d(0, calc(100vh + 100px), 0) rotate(375deg) scale(0.6);
    opacity: 0;
  }
}

/* Glow animation keyframes removed as requested */

/* Floating particle animation keyframes removed as requested */

/* Scroll Animations */
.mobile-app-section .fade-in {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.8s ease forwards;
}

.mobile-app-section.animate-in .fade-in {
  animation-play-state: running;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Additional Animations */
@keyframes pulseGlow {

  0%,
  100% {
    box-shadow: 0 4px 15px rgba(251, 209, 45, 0.3);
  }

  50% {
    box-shadow: 0 6px 25px rgba(251, 209, 45, 0.5);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes countUp {
  from {
    transform: scale(0.8);
    opacity: 0;
  }

  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* Enhanced Transitions */
.stat-item {
  transition: all 0.3s ease;
  cursor: pointer;
}

.stat-item:hover {
  transform: translateY(-3px);
}

.stat-number {
  animation: countUp 0.6s ease forwards;
}

/* Staggered animations for benefit items */
.benefit-item:nth-child(1) {
  animation-delay: 0.1s;
}

.benefit-item:nth-child(2) {
  animation-delay: 0.2s;
}

.benefit-item:nth-child(3) {
  animation-delay: 0.3s;
}

.benefit-item:nth-child(4) {
  animation-delay: 0.4s;
}

/* Staggered animations for stat items */
.stat-item:nth-child(1) {
  animation-delay: 0.5s;
}

.stat-item:nth-child(2) {
  animation-delay: 0.6s;
}

.stat-item:nth-child(3) {
  animation-delay: 0.7s;
}

/* Platform Recommendation */
.app-download-btn.recommended {
  background: linear-gradient(135deg, #fbd12d 0%, #f4c430 100%);
  box-shadow: 0 6px 25px rgba(251, 209, 45, 0.5);
  transform: scale(1.05);
}

.app-download-btn.recommended::after {
  content: 'ÖNERİLEN';
  position: absolute;
  top: -8px;
  right: -8px;
  background: #2b2b4f;
  color: #fbd12d;
  font-size: 0.7rem;
  font-weight: 700;
  padding: 2px 8px;
  border-radius: 12px;
  border: 2px solid #fbd12d;
}

/* Responsive Design */
@media (max-width: 768px) {
  .mobile-app-section {
    padding: 60px 0;
  }

  .mobile-app-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .mobile-app-title {
    font-size: 2.5rem;
  }

  .mobile-app-subtitle {
    font-size: 1.1rem;
  }

  .mobile-app-buttons {
    flex-direction: row;
    justify-content: center;
    flex-wrap: wrap;
  }

  .app-download-btn {
    flex: 1;
    min-width: 200px;
  }

  /* Adjust mobile-specific elements */
  .app-stats {
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    padding: 20px 15px;
  }

  .stat-number {
    font-size: 1.5rem;
  }

  .stat-label {
    font-size: 0.8rem;
  }

  .benefits-grid {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .benefit-item {
    padding: 12px;
  }



  /* Hide the desktop phone image container on mobile */
  .mobile-app-image.desktop {
    display: none;
  }

  /* Show the mobile phone image container on mobile */
  .mobile-app-image.mobile {
    display: flex;
  }

  /* Show mobile phone image */
  .mobile-phone-image {
    display: block;
    text-align: center;
    margin-top: 30px;
  }

  .mobile-phone-img {
    width: 200px;
    height: auto;
    filter: drop-shadow(0 15px 30px rgba(0, 0, 0, 0.3));
  }

  /* Adjust money rain for mobile */
  .money-rain {
    left: -25%;
    width: 150%;
  }

  .money-bill {
    width: 25px;
    height: 16px;
    font-size: 11px;
  }

  .money-bill::before {
    font-size: 12px;
  }

  /* Reduce animation complexity on mobile */
  .money-bill:nth-child(n) {
    animation-duration: 2.5s !important;
  }

  .money-bill:nth-child(even) {
    animation-name: fallDownOptimized !important;
  }
}

@media (max-width: 480px) {
  .mobile-app-buttons {
    flex-direction: column;
  }

  .app-download-btn {
    width: 100%;
  }

  .mobile-app-title {
    font-size: 2rem;
  }

  .phone-mockup {
    max-width: 200px;
  }
}
