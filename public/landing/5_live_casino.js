// Live Casino Section JavaScript
window.$LANDING.onLifecycle({
  selector: `.makrobet-landing`,
  page: /^\/\w+$/,
  onMount: (_, __, kill) => {
    // Configuration
    const GAMES_API = `${window.origin}/odin/api/user/casinoapi/getReservedGames`
    const SLIDER_API = `https://pn17.pfnow100.com/api/tr/consumer`
    const SLIDER_TYPE = 38 // Live casino slider type
    const SLIDER_DEVICE_TYPE = window.origin.includes('//m.') ? 2 : 1
    const IMAGE_BASE_URL = window.origin

    // State
    let games = []
    let gameSlides = []

    // Function to parse custom_class string
    function parseCustomClass(customClass) {
      if (!customClass) return {}

      const result = {}
      const pairs = customClass.split(';')

      pairs.forEach(pair => {
        const [key, value] = pair.split('=')
        if (key && value) {
          result[key.trim()] = value.trim()
        }
      })

      return result
    }

    // Function to fetch game slides
    async function fetchGameSlides() {
      try {
        console.log(`API'den canlı casino slaytları alınıyor...`)

        const response = await fetch(SLIDER_API, {
          method: `GET`,
          headers: {
            [`Content-Type`]: `application/json`
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const data = await response.json()

        if (!data.sliders || !Array.isArray(data.sliders)) {
          throw new Error(`Invalid response format`)
        }

        return data.sliders.filter(s => s.type === SLIDER_TYPE && s.m_t === SLIDER_DEVICE_TYPE)
      } catch (error) {
        console.error(`Canlı casino slaytları alınırken hata:`, error)
        return []
      }
    }

    // Function to fetch games (both slots and live casino)
    async function fetchGames() {
      try {
        console.log(`API'den oyunlar alınıyor (slots ve live casino)...`)

        // Fetch both slots and live casino games in parallel
        const [slotsResponse, liveCasinoResponse] = await Promise.all([
          fetch(GAMES_API, {
            method: `POST`,
            headers: {
              [`Content-Type`]: `application/json`
            },
            body: JSON.stringify({
              requestBody: {
                currencyId: 1,
                gameType: `casino`
              },
              languageId: 1,
              device: `d`
            })
          }),
          fetch(GAMES_API, {
            method: `POST`,
            headers: {
              [`Content-Type`]: `application/json`
            },
            body: JSON.stringify({
              requestBody: {
                currencyId: 1,
                gameType: `liveCasino`
              },
              languageId: 1,
              device: `d`
            })
          })
        ])

        if (!slotsResponse.ok || !liveCasinoResponse.ok) {
          throw new Error(`HTTP error! slots: ${slotsResponse.status}, live: ${liveCasinoResponse.status}`)
        }

        const [slotsData, liveCasinoData] = await Promise.all([slotsResponse.json(), liveCasinoResponse.json()])

        // Combine both game types
        const slotsGames = Array.isArray(slotsData.data?.games) ? slotsData.data.games : []
        const liveGames = Array.isArray(liveCasinoData.data?.games) ? liveCasinoData.data.games : []
        const allGames = [...slotsGames, ...liveGames]

        console.log(`${slotsGames.length} slot oyunu ve ${liveGames.length} canlı casino oyunu yüklendi`)
        return allGames
      } catch (error) {
        console.error(`Oyunlar alınırken hata:`, error)
        return []
      }
    }

    // Function to load and display live casino games
    async function loadLiveCasinoGames() {
      try {
        // Load games and slides in parallel
        const [gamesData, slidesData] = await Promise.all([fetchGames(), fetchGameSlides()])

        games = gamesData
        gameSlides = slidesData

        console.log(`${gameSlides.length} canlı casino slaytı yüklendi`)

        // Map slides to games like tournaments
        const enhancedGames = gameSlides
          .map(slide => {
            const customData = parseCustomClass(slide.custom_class)

            const gameData = games.find(g => g.id.toString() === customData.id)
            if (!gameData) return null

            return {
              ...gameData,
              image: slide.path.startsWith('http') ? slide.path : `${IMAGE_BASE_URL}${slide.path}`
            }
          })
          .filter(Boolean)

        if (enhancedGames.length === 0) {
          enhancedGames.push(
            ...games
              .filter(g => g.gameType === `Live Casino`)
              .slice(0, 20)
              .map(e => ({
                ...e,
                image: `/cdn/common/assets/images/livecasino/300x200/${e.id}.jpg`
              }))
          )
        }

        // Function to create game card with enhanced image and live indicator
        function createGameCard(game) {
          const liveIndicator = window.GameSliderUtils.createLiveIndicator()
          return window.GameSliderUtils.createGameCard(game, game.image, [liveIndicator])
        }

        // Create slider with enhanced games
        const liveCasinoSlider = window.GameSlider({
          sectionId: `live-casino`,
          trackId: `live-casino-track`,
          prevArrowId: `live-prev-arrow`,
          nextArrowId: `live-next-arrow`,
          games: enhancedGames,
          createCardFunction: createGameCard,
          navigationPath: window.location.origin.includes('m.')
            ? `/games/livecasino/detail/:id/:limit`
            : `/games/livecasino/detail/normal/:id/:limit`,
          noItemsMessage: `No live casino games found at the moment.`
        })

        liveCasinoSlider.renderGames()

        console.log(`${enhancedGames.length} canlı casino oyunu yüklendi (slayt ile geliştirilmiş)`)
      } catch (error) {
        console.error(`Canlı casino oyunları yüklenirken hata:`, error)
      }
    }

    // Initialize
    loadLiveCasinoGames()

    window.addEventListener(
      '@makrobet/unload/landing',
      () => {
        kill()
      },
      { once: true }
    )
  }
})
