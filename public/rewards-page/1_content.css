@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&display=swap');

:root {
  --primary-color: 251, 209, 45;
  --secondary-color: 43, 43, 79;
}

.makrobet-rewards-container {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  background:
    /* Primary ambient orbs */
    radial-gradient(ellipse 600px 400px at 15% 25%,
      rgba(var(--primary-color), 0.12) 0%,
      transparent 50%),
    radial-gradient(ellipse 500px 600px at 85% 75%, rgba(var(--secondary-color), 0.15) 0%, transparent 50%),
    radial-gradient(ellipse 400px 300px at 70% 20%, rgba(var(--primary-color), 0.08) 0%, transparent 60%),
    radial-gradient(ellipse 350px 450px at 20% 80%, rgba(var(--secondary-color), 0.1) 0%, transparent 55%),
    radial-gradient(ellipse 300px 200px at 50% 10%, rgba(var(--primary-color), 0.06) 0%, transparent 65%),
    radial-gradient(ellipse 250px 350px at 90% 50%, rgba(var(--secondary-color), 0.08) 0%, transparent 60%),
    /* Deep space gradient with more complexity */
    linear-gradient(135deg,
      #0a0a1a 0%,
      #1a1a2e 15%,
      #16213e 30%,
      #1e1e3f 45%,
      #2a2a4a 60%,
      #1e1e3f 75%,
      #16213e 85%,
      #0f0f23 100%),
    /* Base atmospheric layer */
    linear-gradient(45deg, #0a0a1a, #1a1a2e);
  color: #ffffff;
  position: relative;
  overflow-x: hidden;
  overflow-y: hidden;
}

.makrobet-rewards-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    /* Floating particle system */
    radial-gradient(circle 4px at 12% 20%,
      rgba(var(--primary-color), 0.8) 0%,
      transparent 50%),
    radial-gradient(circle 2px at 88% 25%, rgba(var(--secondary-color), 0.6) 0%, transparent 50%),
    radial-gradient(circle 3px at 25% 75%, rgba(var(--primary-color), 0.7) 0%, transparent 50%),
    radial-gradient(circle 1px at 75% 80%, rgba(var(--secondary-color), 0.9) 0%, transparent 50%),
    radial-gradient(circle 2px at 45% 15%, rgba(var(--primary-color), 0.5) 0%, transparent 50%),
    radial-gradient(circle 3px at 65% 60%, rgba(var(--secondary-color), 0.7) 0%, transparent 50%),
    radial-gradient(circle 1px at 35% 40%, rgba(var(--primary-color), 0.8) 0%, transparent 50%),
    radial-gradient(circle 2px at 55% 85%, rgba(var(--secondary-color), 0.6) 0%, transparent 50%),
    radial-gradient(circle 1px at 15% 55%, rgba(var(--primary-color), 0.9) 0%, transparent 50%),
    radial-gradient(circle 2px at 85% 45%, rgba(var(--secondary-color), 0.5) 0%, transparent 50%);
  background-size: 200px 200px, 150px 150px, 250px 250px, 100px 100px, 180px 180px, 220px 220px, 120px 120px,
    160px 160px, 90px 90px, 140px 140px;
  pointer-events: none;
  animation: floatingParticles 25s ease-in-out infinite;
}

@keyframes floatingParticles {

  0%,
  100% {
    transform: translateX(0) translateY(0);
    opacity: 1;
  }

  20% {
    transform: translateX(12px) translateY(-8px);
    opacity: 0.8;
  }

  40% {
    transform: translateX(-6px) translateY(15px);
    opacity: 0.9;
  }

  60% {
    transform: translateX(18px) translateY(-12px);
    opacity: 0.7;
  }

  80% {
    transform: translateX(-10px) translateY(6px);
    opacity: 0.85;
  }
}

.makrobet-rewards-container * {
  box-sizing: border-box;
}

.content-wrapper {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
}

/* Page Header */
.page-header {
  text-align: left;
  margin-bottom: 48px;
  padding: 32px 0 0 0;
}

.page-title {
  font-size: clamp(32px, 5vw, 48px);
  font-weight: 900;
  color: rgb(var(--primary-color));
  margin: 0 0 16px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: 0.5px;
  line-height: 1.2;
}

.page-description {
  font-size: clamp(16px, 2.5vw, 20px);
  color: rgba(255, 255, 255, 0.85);
  margin: 0;
  line-height: 1.5;
  font-weight: 400;
  letter-spacing: 0.3px;
}

/* Section Styling */
.rewards-section {
  margin-bottom: 56px;
}

.section-title {
  font-size: clamp(24px, 4vw, 32px);
  font-weight: 700;
  text-align: left;
  margin-bottom: 40px;
  color: rgb(var(--primary-color));
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, rgb(var(--primary-color)), transparent);
  border-radius: 2px;
}

.rewards-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 32px;
  width: 100%;
}

.reward-card {
  background: linear-gradient(135deg, rgba(var(--secondary-color), 0.9) 0%, rgba(30, 30, 63, 0.9) 100%);
  border-radius: 20px;
  padding: 32px 28px;
  text-align: center;
  position: relative;
  overflow: hidden;
  border: 2px solid rgba(var(--primary-color), 0.4);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), 0 4px 16px rgba(0, 0, 0, 0.2);
  min-height: 480px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.reward-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg,
      rgba(var(--primary-color), 0.08) 0%,
      transparent 30%,
      rgba(var(--primary-color), 0.05) 70%,
      transparent 100%);
  z-index: 1;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  border-radius: 20px;
}

/* Diagonal sweep light animation */
.reward-card .sweep-light {
  position: absolute;
  top: -50%;
  left: -150%;
  width: 50%;
  height: 200%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transform: rotate(45deg);
  transition: left 0.4s ease;
  z-index: 10;
  pointer-events: none;
}

.reward-card:hover {
  transform: translateY(-3px) scale(1.01);
  border-color: rgba(var(--primary-color), 0.6);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.5), 0 6px 20px rgba(0, 0, 0, 0.3);
}

.reward-card:hover::before {
  opacity: 1;
}

.reward-card:hover .sweep-light {
  left: 200%;
}

.reward-card h3 {
  font-size: 20px;
  font-weight: 700;
  margin: 0;
  color: rgb(var(--primary-color));
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  z-index: 2;
}

.reward-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 32px;
  height: 100%;
  position: relative;
  z-index: 2;
  padding: 8px 0;
}

.reward-header {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.reward-description {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
  margin: 0;
  text-align: center;
  font-weight: 400;
  letter-spacing: 0.2px;
}

.reward-icon-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 220px;
  height: 220px;
  margin: 0;
}

/* Gold Coin Stack */
.reward-icon {
  position: relative;
  width: 200px;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.coin-stack {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.coin-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  filter: drop-shadow(0 6px 16px rgba(0, 0, 0, 0.4));
  transition: all 0.3s ease;
}

/* Coin image animation */
@keyframes coinFloat {

  0%,
  100% {
    transform: translateY(0) scale(1);
  }

  50% {
    transform: translateY(-8px) scale(1.02);
  }
}

.coin-image {
  animation: coinFloat 3s ease-in-out infinite;
}

/* Welcome bonus hover effect */
.welcome-bonus-banner:hover {
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.5), 0 6px 20px rgba(0, 0, 0, 0.3);
  transform: translateY(-2px);
}



.reward-amount {
  font-size: clamp(28px, 4vw, 36px);
  font-weight: 900;
  color: rgb(var(--primary-color));
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  margin: 8px 0 0 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.reward-amount .currency-symbol {
  font-size: 0.8em;
  opacity: 0.9;
}

.reward-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}



.claim-btn {
  background: linear-gradient(135deg, rgb(var(--primary-color)) 0%, #e6c200 100%);
  color: #1a1a2e;
  border: none;
  padding: 16px 40px;
  font-size: 16px;
  font-weight: 700;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: none;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 16px rgba(var(--primary-color), 0.3), 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
  overflow: hidden;
  width: 100%;
  margin-top: auto;
}

.claim-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.claim-btn:hover::before {
  left: 100%;
}

.claim-btn:hover:not(:disabled) {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 24px rgba(var(--primary-color), 0.4), 0 4px 12px rgba(0, 0, 0, 0.2);
  background: linear-gradient(135deg, #ffe42d 0%, rgb(var(--primary-color)) 100%);
  color: #1a1a2e;
}

/* Claimed/Disabled state */
.claim-btn.claimed,
.claim-btn:disabled {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  color: #9ca3af;
  cursor: not-allowed;
  opacity: 0.6;
  transform: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.claim-btn.claimed:hover,
.claim-btn:disabled:hover {
  transform: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
}



/* Welcome Bonus Banner */
.welcome-bonus-banner {
  background: linear-gradient(135deg, rgba(var(--secondary-color), 0.9) 0%, rgba(30, 30, 63, 0.9) 100%);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 40px 32px;
  text-align: center;
  margin-top: 32px;
  margin-bottom: 56px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), 0 4px 16px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

/* Post-claim styling */
.welcome-bonus-banner.bonus-claimed-state {
  border: 2px solid rgba(255, 255, 255, 0.15) !important;
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.3),
    0 4px 16px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  animation: none !important;
}

.welcome-bonus-banner::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg,
      rgba(var(--primary-color), 0.08) 0%,
      transparent 30%,
      rgba(var(--primary-color), 0.05) 70%,
      transparent 100%);
  z-index: 1;
  border-radius: 20px;
  pointer-events: none;
}

/* Remove overlay when claimed */
.welcome-bonus-banner.bonus-claimed-state::before {
  display: none;
}

.welcome-bonus-banner h2 {
  font-size: clamp(24px, 4vw, 36px);
  font-weight: 700;
  margin: 0 0 12px 0;
  color: rgb(var(--primary-color));
  text-transform: uppercase;
  position: relative;
  z-index: 2;
  letter-spacing: 0.5px;
}

.welcome-bonus-banner .amount {
  font-size: clamp(36px, 6vw, 56px);
  font-weight: 900;
  color: rgb(var(--primary-color));
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  margin: 16px 0;
  position: relative;
  z-index: 2;
}

.welcome-bonus-btn {
  background: linear-gradient(135deg, #ffd700 0%, #ffe42d 100%);
  color: #140608;
  padding: 16px 48px;
  font-size: 16px;
  font-weight: 700;
  border: none;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 3px 15px rgba(var(--primary-color), 0.35), 0 2px 8px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 2;
  overflow: hidden;
}

.welcome-bonus-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.welcome-bonus-btn:hover::before {
  left: 100%;
}

.welcome-bonus-btn:hover:not(:disabled) {
  transform: translateY(-2px) scale(1.01);
  box-shadow: 0 8px 24px rgba(var(--primary-color), 0.4);
  background: linear-gradient(45deg, rgba(var(--primary-color), 1.1), rgb(var(--primary-color)));
}

/* Disabled/Claimed state */
.welcome-bonus-btn:disabled,
.welcome-bonus-btn.claimed {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  color: #9ca3af;
  cursor: not-allowed;
  opacity: 0.7;
  transform: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.welcome-bonus-btn:disabled:hover,
.welcome-bonus-btn.claimed:hover {
  transform: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
}



/* Promo Code Section */
.promo-section {
  background: linear-gradient(135deg, rgba(var(--secondary-color), 0.8) 0%, rgba(30, 30, 63, 0.8) 100%);
  border-radius: 16px;
  padding: 24px 32px;
  margin-bottom: 48px;
  border: 1px solid rgba(var(--primary-color), 0.3);
  position: relative;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25), 0 4px 12px rgba(0, 0, 0, 0.15);
  text-align: center;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.promo-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg,
      rgba(var(--primary-color), 0.05) 0%,
      transparent 30%,
      rgba(var(--primary-color), 0.03) 70%,
      transparent 100%);
  z-index: 1;
  border-radius: 16px;
  pointer-events: none;
}

.promo-section h3 {
  font-size: clamp(18px, 2.5vw, 22px);
  font-weight: 700;
  margin: 0 0 20px 0;
  text-align: center;
  color: rgb(var(--primary-color));
  text-transform: none;
  letter-spacing: 0.3px;
  position: relative;
  z-index: 2;
}

.promo-input-wrapper {
  display: flex;
  gap: 16px;
  width: 100%;
  position: relative;
  z-index: 2;
  align-items: center;
}

.apply-btn {
  background: linear-gradient(135deg, rgb(var(--primary-color)) 0%, #e6c200 100%);
  color: #1a1a2e;
  border: none;
  padding: 16px 32px;
  font-size: 16px;
  font-weight: 700;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: none;
  letter-spacing: 0.5px;
  box-shadow:
    0 4px 16px rgba(var(--primary-color), 0.3),
    0 2px 8px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  white-space: nowrap;
  min-width: 120px;
  height: 56px;
  position: relative;
  overflow: hidden;
}

.apply-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.apply-btn:hover::before {
  left: 100%;
}

.apply-btn:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 24px rgba(var(--primary-color), 0.4), 0 4px 12px rgba(0, 0, 0, 0.2);
  background: linear-gradient(135deg, #ffe42d 0%, rgb(var(--primary-color)) 100%);
  color: #1a1a2e;
}

.promo-message {
  text-align: center;
  padding: 12px 20px;
  border-radius: 12px;
  margin-top: 16px;
  font-size: 14px;
  font-weight: 600;
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
  width: 100%;
  opacity: 0;
  transform: translateY(-10px);
}

.promo-message.success {
  background: rgba(76, 217, 100, 0.15);
  color: #4cd964;
  border: 1px solid rgba(76, 217, 100, 0.4);
  opacity: 1;
  transform: translateY(0);
}

.promo-message.error {
  background: rgba(255, 59, 48, 0.15);
  color: #ff3b30;
  border: 1px solid rgba(255, 59, 48, 0.4);
  opacity: 1;
  transform: translateY(0);
}

/* VIP Section */
.vip-section {
  margin-bottom: 40px;
}

/* VIP Header Layout */
.vip-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  gap: 24px;
}

.vip-header-content {
  flex: 1;
}

.vip-title {
  font-size: clamp(28px, 5vw, 40px);
  font-weight: 800;
  text-align: left;
  margin: 0 0 16px 0;
  color: rgb(var(--primary-color));
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.vip-subtitle {
  text-align: left;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  line-height: 1.6;
}

/* VIP Navigation Controls */
.vip-nav-controls {
  display: flex;
  gap: 12px;
  align-items: center;
  align-self: flex-end;
  flex-shrink: 0;
}

/* VIP Slider Wrapper */
.vip-slider-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  overflow: hidden;
}

.vip-ranks-container {
  overflow: hidden;
  padding: 32px 0;
  border-radius: 16px;
  background: transparent;
  cursor: grab;
  user-select: none;
  /* Fixed width will be set by JavaScript based on screen size */
  width: 1350px;
  /* Default desktop: (180 × 7) + (15 × 6) = 1350px */
  margin: 0;
  /* Left-aligned, no centering */
}

.vip-ranks-container:active {
  cursor: grabbing;
}

.vip-ranks-grid {
  display: flex;
  gap: 15px;
  transition: transform 0.3s ease;
  will-change: transform;
}

/* Navigation Arrows */
.vip-nav-arrow {
  background: linear-gradient(135deg, rgb(var(--primary-color)) 0%, #e6c200 100%);
  border: none;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #1a1a2e;
  box-shadow: 0 4px 16px rgba(var(--primary-color), 0.3);
  z-index: 10;
  flex-shrink: 0;
}

.vip-nav-arrow:hover:not(:disabled) {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(var(--primary-color), 0.4);
}

.vip-nav-arrow:disabled {
  opacity: 0.3;
  cursor: not-allowed;
  transform: none;
}

.vip-nav-arrow svg {
  width: 20px;
  height: 20px;
}

.vip-rank-card {
  background: linear-gradient(135deg, rgba(var(--secondary-color), 0.8) 0%, #161632 100%);
  border-radius: 16px;
  padding: 20px 16px;
  text-align: center;
  width: 180px;
  flex-shrink: 0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(var(--primary-color), 0.1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2), 0 2px 0px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.vip-rank-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg,
      rgba(var(--primary-color), 0.05) 0%,
      transparent 30%,
      rgba(var(--primary-color), 0.03) 70%,
      transparent 100%);
  z-index: 1;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  border-radius: 16px;
}

.vip-rank-card::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg,
      rgba(var(--primary-color), 0.3) 0%,
      transparent 25%,
      rgba(var(--primary-color), 0.2) 50%,
      transparent 75%,
      rgba(var(--primary-color), 0.3) 100%);
  z-index: -1;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  border-radius: 18px;
}

.vip-rank-card:hover::before {
  opacity: 1;
}

.vip-rank-card:hover::after {
  opacity: 1;
}

.vip-rank-card:hover {
  border-color: rgba(var(--primary-color), 0.4);
}

.vip-rank-image {
  width: 80px;
  height: 80px;
  margin: 0 auto 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.vip-rank-image::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle, rgba(var(--primary-color), 0.1) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.vip-rank-card:hover .vip-rank-image::before {
  opacity: 1;
}

.vip-rank-image img {
  max-width: 80%;
  max-height: 80%;
  object-fit: contain;
  position: relative;
  z-index: 2;
  transition: transform 0.3s ease;
}

.vip-rank-card:hover .vip-rank-image img {
  transform: scale(1.1);
}

.vip-rank-name {
  font-size: 16px;
  font-weight: 700;
  color: rgb(var(--primary-color));
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  position: relative;
  z-index: 2;
}

.vip-rank-benefits {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 12px;
  line-height: 1.4;
  position: relative;
  z-index: 2;
}

.view-perks-btn {
  background: transparent;
  color: rgb(var(--primary-color));
  border: 1px solid rgba(var(--primary-color), 0.5);
  padding: 6px 16px;
  font-size: 11px;
  font-weight: 600;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.2px;
  position: relative;
  z-index: 2;
}

.view-perks-btn:hover {
  background: rgb(var(--primary-color));
  color: #140608;
  border-color: rgb(var(--primary-color));
  box-shadow: 0 4px 12px rgba(var(--primary-color), 0.3);
  transform: translateY(-1px);
}

/* Special MakroBlack Card */
.vip-rank-card.makroblack {
  background: linear-gradient(145deg, #000000 0%, #1a1a1a 100%);
  border: 1px solid rgb(var(--primary-color));
}

.vip-rank-card.makroblack .vip-rank-image {
  background: radial-gradient(circle, rgba(var(--primary-color), 0.2) 0%, transparent 70%);
  border-color: rgba(var(--primary-color), 0.3);
}

.vip-rank-card.makroblack .view-perks-btn {
  background: rgb(var(--primary-color));
  color: #140608;
  border-color: rgb(var(--primary-color));
}

.vip-rank-card.makroblack .view-perks-btn:hover {
  background: rgba(var(--primary-color), 0.9);
  transform: translateY(-1px) scale(1.02);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .content-wrapper {
    padding: 0 20px;
  }

  .rewards-grid {
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    width: 100%;
  }

  .reward-card {
    min-height: 450px;
    padding: 28px 24px;
  }



  .vip-ranks-grid {
    gap: 16px;
  }

  .vip-rank-card {
    width: 180px;
    padding: 18px 14px;
  }

  .vip-ranks-grid {
    gap: 15px;
  }
}

@media (max-width: 768px) {
  .content-wrapper {
    padding: 0 16px;
  }

  .page-header {
    margin-bottom: 32px;
    padding: 24px 0 0 0;
  }

  .rewards-grid {
    grid-template-columns: 1fr;
    gap: 20px;
    width: 100%;
  }

  .reward-card {
    padding: 24px 20px;
    min-height: 420px;
  }

  .reward-icon-wrapper {
    width: 180px;
    height: 180px;
  }

  .reward-icon {
    width: 160px;
    height: 160px;
  }

  .reward-content {
    gap: 24px;
  }



  .promo-input-wrapper {
    flex-direction: column;
    gap: 12px;
  }

  .promo-section {
    padding: 28px 20px;
    margin-bottom: 32px;
  }

  .welcome-bonus-banner {
    padding: 32px 24px;
  }

  .vip-rank-card {
    width: 180px;
    padding: 16px 12px;
  }

  .vip-rank-image {
    width: 70px;
    height: 70px;
    margin-bottom: 10px;
  }

  .vip-ranks-grid {
    gap: 15px;
  }

  .vip-ranks-container {
    margin: 0 -16px;
    padding: 0 16px 16px;
  }
}

@media (max-width: 480px) {
  .content-wrapper {
    padding: 0 12px;
  }

  .page-header {
    margin-bottom: 32px;
    padding: 24px 0 0 0;
  }

  .reward-card {
    padding: 20px 16px;
    border-radius: 16px;
  }

  .reward-icon-wrapper {
    width: 160px;
    height: 160px;
  }

  .reward-icon {
    width: 140px;
    height: 140px;
  }

  .reward-content {
    gap: 24px;
  }

  .reward-amount {
    font-size: clamp(20px, 5vw, 28px);
  }

  .promo-section {
    padding: 24px 16px;
    border-radius: 16px;
    margin-bottom: 28px;
  }

  .welcome-bonus-banner {
    padding: 24px 20px;
    border-radius: 20px;
  }

  .vip-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 24px;
  }

  .vip-nav-controls {
    gap: 8px;
    align-self: flex-end;
  }

  .vip-nav-arrow {
    width: 32px;
    height: 32px;
  }

  .vip-nav-arrow svg {
    width: 14px;
    height: 14px;
  }

  .vip-rank-card {
    width: 180px;
    padding: 14px 10px;
    border-radius: 14px;
  }

  .vip-rank-image {
    width: 60px;
    height: 60px;
    margin-bottom: 8px;
  }

  .vip-rank-name {
    font-size: 13px;
    margin-bottom: 6px;
  }

  .vip-rank-benefits {
    font-size: 10px;
    margin-bottom: 10px;
  }

  .view-perks-btn {
    padding: 5px 14px;
    font-size: 10px;
  }
}

/* VIP Modal */
.vip-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: none;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.vip-modal.active {
  display: flex;
}

.vip-modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(4px);
}

.vip-modal-content {
  background: linear-gradient(135deg, rgb(var(--secondary-color)) 0%, #1e1e3f 100%);
  border-radius: 20px;
  border: 2px solid rgba(var(--primary-color), 0.3);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  max-width: 600px;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
  position: relative;
  z-index: 1001;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }

  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.vip-modal-header {
  padding: 24px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(var(--primary-color), 0.2);
  margin-bottom: 24px;
}

.vip-modal-title {
  font-size: 24px;
  font-weight: 700;
  color: rgb(var(--primary-color));
  margin: 0;
}

.vip-modal-close {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.2s ease;
  height: 40px;
}

.vip-modal-close:focus {
  background: rgba(255, 255, 255, 0.1);
  color: rgb(var(--primary-color));
}

.vip-modal-close:hover {
  background: rgba(255, 255, 255, 0.1);
  color: rgb(var(--primary-color));
}

.vip-modal-body {
  padding: 0 24px 24px;
  max-height: 60vh;
  overflow-y: auto;
}

.vip-modal-tier-name {
  font-size: 20px;
  font-weight: 700;
  color: rgb(var(--primary-color));
  margin-bottom: 16px;
  text-align: center;
}

.vip-modal-perks-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.vip-modal-perk {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
}

.vip-modal-perk:last-child {
  border-bottom: none;
}

.vip-modal-perk-icon {
  width: 20px;
  height: 20px;
  color: rgb(var(--primary-color));
  flex-shrink: 0;
}

/* Responsive Modal */
@media (max-width: 768px) {
  .vip-modal {
    padding: 16px;
  }

  .vip-modal-content {
    max-height: 90vh;
  }

  .vip-modal-header {
    padding: 20px 20px 0;
  }

  .vip-modal-body {
    padding: 0 20px 20px;
  }

  .vip-modal-title {
    font-size: 20px;
  }
}

/* Responsive VIP Slider */
@media (max-width: 1024px) {
  .vip-nav-arrow {
    width: 40px;
    height: 40px;
  }

  .vip-nav-arrow svg {
    width: 18px;
    height: 18px;
  }

  .vip-ranks-grid {
    gap: 15px;
  }
}

@media (max-width: 768px) {
  .vip-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .vip-nav-controls {
    align-self: flex-end;
  }

  .vip-nav-arrow {
    width: 36px;
    height: 36px;
  }

  .vip-nav-arrow svg {
    width: 16px;
    height: 16px;
  }

  .vip-ranks-container {
    padding: 32px 0;
    width: 1350px;
    /* Desktop: (180 × 7) + (15 × 6) = 1350px */
  }

  .vip-rank-card {
    width: 180px;
  }
}

/* Tablet: Show 5 cards */
@media screen and (max-width: 1199px) and (min-width: 768px) {
  .vip-ranks-container {
    width: 960px;
    /* Tablet: (180 × 5) + (15 × 4) = 960px */
  }
}

/* Mobile: Show 3 cards */
@media screen and (max-width: 767px) {
  .vip-ranks-container {
    width: 570px;
    /* Mobile: (180 × 3) + (15 × 2) = 570px */
  }

  .makrobet-rewards-container::before {
    animation: none;
  }
}

/* Happy Hours Section Styles */
.happy-hours-section {
  margin: 40px 0;
  padding: 32px;
  background: linear-gradient(135deg, rgba(var(--primary-color), 0.05) 0%, rgba(var(--secondary-color), 0.1) 100%);
  border-radius: 20px;
  border: 1px solid rgba(var(--primary-color), 0.1);
}

.happy-hours-section h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: rgb(var(--primary-color));
  text-align: center;
  margin-bottom: 12px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.happy-hours-section .section-description {
  text-align: center;
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 32px;
  font-weight: 500;
}

.happy-hours-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.happy-hour-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border: 1px solid rgba(var(--primary-color), 0.2);
  border-radius: 16px;
  padding: 24px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.happy-hour-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, rgb(var(--primary-color)) 0%, rgba(var(--primary-color), 0.6) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.happy-hour-card:hover {
  transform: translateY(-8px);
  border-color: rgba(var(--primary-color), 0.4);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.happy-hour-card:hover::before {
  opacity: 1;
}

.happy-hour-time {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(var(--primary-color), 0.2);
}

.time-label {
  font-size: 1.1rem;
  font-weight: 700;
  color: rgb(var(--primary-color));
  margin-bottom: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.time-range {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
  background: rgba(var(--primary-color), 0.1);
  padding: 4px 12px;
  border-radius: 12px;
  border: 1px solid rgba(var(--primary-color), 0.2);
}

.happy-hour-bonus {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
}

.bonus-icon {
  font-size: 3rem;
  line-height: 1;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
  animation: float 3s ease-in-out infinite;
}

@keyframes float {

  0%,
  100% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-6px);
  }
}

.bonus-details {
  flex: 1;
}

.bonus-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 8px;
  line-height: 1.2;
}

.bonus-amount {
  font-size: 1.8rem;
  font-weight: 700;
  color: rgb(var(--primary-color));
  display: flex;
  align-items: baseline;
  gap: 2px;
}

.bonus-amount .currency-symbol {
  font-size: 1.4rem;
  opacity: 0.8;
}

.happy-hour-btn {
  width: 100%;
  padding: 14px 24px;
  font-size: 1rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-radius: 12px;
  background: linear-gradient(135deg, rgb(var(--primary-color)) 0%, rgba(var(--primary-color), 0.8) 100%);
  color: rgb(var(--secondary-color));
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.happy-hour-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.happy-hour-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(var(--primary-color), 0.3);
  background: linear-gradient(135deg, rgba(var(--primary-color), 1.1) 0%, rgb(var(--primary-color)) 100%);
}

.happy-hour-btn:hover::before {
  left: 100%;
}

.happy-hour-btn:active {
  transform: translateY(0);
  box-shadow: 0 4px 12px rgba(var(--primary-color), 0.2);
}

.happy-hour-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.happy-hour-btn.claimed {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
}

.happy-hour-btn.unavailable {
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
  color: rgba(255, 255, 255, 0.7);
  cursor: not-allowed;
}

/* Happy Hours Responsive Design */
@media (max-width: 768px) {
  .happy-hours-section {
    margin: 24px 0;
    padding: 20px;
  }

  .happy-hours-section h2 {
    font-size: 2rem;
  }

  .happy-hours-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .happy-hour-card {
    padding: 20px;
  }

  .bonus-icon {
    font-size: 2.5rem;
  }

  .bonus-title {
    font-size: 1.1rem;
  }

  .bonus-amount {
    font-size: 1.6rem;
  }

  .happy-hour-btn {
    padding: 12px 20px;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .happy-hours-section {
    padding: 16px;
  }

  .happy-hour-bonus {
    gap: 12px;
  }

  .bonus-icon {
    font-size: 2rem;
  }

  .time-label {
    font-size: 1rem;
  }

  .time-range {
    font-size: 0.8rem;
  }
}

/* Bonus Notification System */
.bonus-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 999999;
  max-width: 400px;
  min-width: 320px;
  opacity: 0;
  transform: translateX(100%) scale(0.95);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
}

.bonus-notification-show {
  opacity: 1;
  transform: translateX(0) scale(1);
  pointer-events: all;
}

.bonus-notification-content {
  background: linear-gradient(135deg,
    rgba(var(--secondary-color), 0.95) 0%,
    rgba(30, 30, 63, 0.95) 100%);
  border: 2px solid rgba(var(--primary-color), 0.3);
  border-radius: 16px;
  padding: 20px;
  display: flex;
  align-items: flex-start;
  gap: 12px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.4),
    0 8px 16px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  position: relative;
  overflow: hidden;
}

.bonus-notification-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(var(--primary-color), 0.05) 0%,
    transparent 50%,
    rgba(var(--primary-color), 0.03) 100%);
  pointer-events: none;
}

.bonus-notification-icon {
  font-size: 24px;
  flex-shrink: 0;
  margin-top: 2px;
  position: relative;
  z-index: 1;
}

.bonus-notification-message {
  flex: 1;
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.4;
  position: relative;
  z-index: 1;
}

.bonus-notification-close {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  font-size: 20px;
  font-weight: bold;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
  flex-shrink: 0;
  position: relative;
  z-index: 1;
}

.bonus-notification-close:hover {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.1);
  transform: scale(1.1);
}

/* Notification type variants */
.bonus-notification-error .bonus-notification-content {
  border-color: rgba(255, 107, 107, 0.4);
}

.bonus-notification-error .bonus-notification-content::before {
  background: linear-gradient(135deg,
    rgba(255, 107, 107, 0.08) 0%,
    transparent 50%,
    rgba(255, 107, 107, 0.05) 100%);
}

.bonus-notification-success .bonus-notification-content {
  border-color: rgba(72, 187, 120, 0.4);
}

.bonus-notification-success .bonus-notification-content::before {
  background: linear-gradient(135deg,
    rgba(72, 187, 120, 0.08) 0%,
    transparent 50%,
    rgba(72, 187, 120, 0.05) 100%);
}

.bonus-notification-info .bonus-notification-content {
  border-color: rgba(66, 153, 225, 0.4);
}

.bonus-notification-info .bonus-notification-content::before {
  background: linear-gradient(135deg,
    rgba(66, 153, 225, 0.08) 0%,
    transparent 50%,
    rgba(66, 153, 225, 0.05) 100%);
}

/* Mobile responsiveness for notifications */
@media (max-width: 480px) {
  .bonus-notification {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
    min-width: auto;
  }

  .bonus-notification-content {
    padding: 16px;
  }

  .bonus-notification-message {
    font-size: 13px;
  }
}

/* Details Button Styles - Subtle and Secondary */
.details-btn,
.welcome-details-btn {
  background: none;
  border: 1px solid rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 0.6);
  padding: 8px 16px;
  font-size: 13px;
  font-weight: 400;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-transform: none;
  letter-spacing: 0;
  width: fit-content;
  align-self: center;
  margin-bottom: 14px;
  opacity: 0.7;
}

.welcome-details-btn {
  margin-bottom: 18px;
  font-size: 14px;
  padding: 9px 18px;
}

.details-btn:hover,
.welcome-details-btn:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.25);
  color: rgba(255, 255, 255, 0.8);
  opacity: 1;
  transform: none;
  box-shadow: none;
}

.details-btn:active,
.welcome-details-btn:active {
  background: rgba(255, 255, 255, 0.08);
  transform: none;
  box-shadow: none;
}

/* Bonus Details Modal */
.bonus-details-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.bonus-details-modal-show {
  opacity: 1;
  visibility: visible;
}

.bonus-details-modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
}

.bonus-details-modal-content {
  background: linear-gradient(135deg,
    rgba(var(--secondary-color), 0.95) 0%,
    rgba(30, 30, 63, 0.95) 100%);
  border: 2px solid rgba(var(--primary-color), 0.3);
  border-radius: 20px;
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  position: relative;
  z-index: 1;
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.5),
    0 10px 20px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  transform: scale(0.9) translateY(20px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.bonus-details-modal-show .bonus-details-modal-content {
  transform: scale(1) translateY(0);
}

.bonus-details-modal-header {
  padding: 24px 28px 20px;
  border-bottom: 1px solid rgba(var(--primary-color), 0.2);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(135deg,
    rgba(var(--primary-color), 0.08) 0%,
    transparent 100%);
}

.bonus-details-modal-title {
  font-size: 20px;
  font-weight: 700;
  color: rgb(var(--primary-color));
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.bonus-details-modal-close {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bonus-details-modal-close:hover {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.1);
  transform: scale(1.1);
}

.bonus-details-modal-body {
  padding: 28px;
  overflow-y: auto;
  max-height: calc(80vh - 120px);
  color: #ffffff;
  line-height: 1.6;
}

/* Markdown content styling */
.bonus-details-modal-body h1 {
  font-size: 24px;
  font-weight: 700;
  color: rgb(var(--primary-color));
  margin: 0 0 20px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.bonus-details-modal-body h2 {
  font-size: 20px;
  font-weight: 600;
  color: rgb(var(--primary-color));
  margin: 24px 0 16px 0;
  border-bottom: 2px solid rgba(var(--primary-color), 0.3);
  padding-bottom: 8px;
}

.bonus-details-modal-body h3 {
  font-size: 18px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin: 20px 0 12px 0;
}

.bonus-details-modal-body p {
  margin: 0 0 16px 0;
  color: rgba(255, 255, 255, 0.8);
}

.bonus-details-modal-body strong {
  color: rgb(var(--primary-color));
  font-weight: 600;
}

.bonus-details-modal-body em {
  color: rgba(255, 255, 255, 0.7);
  font-style: italic;
}

.bonus-details-modal-body code {
  background: rgba(var(--primary-color), 0.1);
  color: rgb(var(--primary-color));
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
}

.bonus-details-modal-body ul {
  margin: 16px 0;
  padding-left: 0;
  list-style: none;
}

.bonus-details-modal-body li {
  margin: 8px 0;
  padding-left: 20px;
  position: relative;
  color: rgba(255, 255, 255, 0.8);
}

.bonus-details-modal-body li::before {
  content: '•';
  color: rgb(var(--primary-color));
  font-weight: bold;
  position: absolute;
  left: 0;
}

/* Mobile responsiveness for modal and buttons */
@media (max-width: 768px) {
  .details-btn,
  .welcome-details-btn {
    margin-bottom: 12px;
    font-size: 12px;
    padding: 7px 14px;
  }

  .welcome-details-btn {
    font-size: 13px;
    padding: 8px 16px;
  }

  .bonus-details-modal-content {
    width: 95%;
    max-height: 85vh;
    border-radius: 16px;
  }

  .bonus-details-modal-header {
    padding: 20px 24px 16px;
  }

  .bonus-details-modal-title {
    font-size: 18px;
  }

  .bonus-details-modal-body {
    padding: 24px 20px;
    max-height: calc(85vh - 100px);
  }

  .bonus-details-modal-body h1 {
    font-size: 20px;
  }

  .bonus-details-modal-body h2 {
    font-size: 18px;
  }

  .bonus-details-modal-body h3 {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .details-btn,
  .welcome-details-btn {
    padding: 6px 12px;
    font-size: 11px;
    margin-bottom: 10px;
  }

  .welcome-details-btn {
    font-size: 12px;
    padding: 7px 14px;
  }

  .bonus-details-modal-content {
    width: 98%;
    max-height: 90vh;
  }

  .bonus-details-modal-header {
    padding: 16px 20px 12px;
  }

  .bonus-details-modal-body {
    padding: 20px 16px;
    max-height: calc(90vh - 80px);
  }
}
