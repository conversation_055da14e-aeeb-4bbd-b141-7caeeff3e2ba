// Rewards Page JavaScript Logic
window.$REWARDS.onLifecycle({
  selector: `.rewards.content`,
  page: /^\/\w+\/pages\/rewards/,
  onMount: (_, __, kill) => {
    console.log('Rewards page mounted')

    // Base URL constant for easy future changes
    const API_URL = 'http://localhost:3000'
    const BASE_URL = 'https://cdn.makroz.org/makrobet/rewards'

    // Replace coin stacks with PNG image
    const initializeRewardIcons = () => {
      const coinStacks = document.querySelectorAll('.coin-stack')
      coinStacks.forEach(coinStack => {
        // Create image element
        const coinImage = document.createElement('img')
        coinImage.src = `${BASE_URL}/coins.png`
        coinImage.alt = 'Reward Coins'
        coinImage.className = 'coin-image'
        coinImage.style.width = '100%'
        coinImage.style.height = '100%'
        coinImage.style.objectFit = 'contain'

        // Replace coin stack with image
        coinStack.innerHTML = ''
        coinStack.appendChild(coinImage)
      })
    }

    // Initialize icons after a short delay to ensure DOM is ready
    setTimeout(initializeRewardIcons, 100)

    // Initialize VIP container drag functionality
    const initVipDrag = () => {
      const container = document.getElementById('vipContainer')
      if (!container) return

      let isDown = false
      let startX

      container.addEventListener('mousedown', e => {
        isDown = true
        container.classList.add('dragging')
        startX = e.pageX
      })

      container.addEventListener('mouseleave', () => {
        isDown = false
        container.classList.remove('dragging')
      })

      container.addEventListener('mouseup', e => {
        if (!isDown) return
        isDown = false
        container.classList.remove('dragging')

        // Calculate drag distance and determine if we should change slides
        const endX = e.pageX
        const dragDistance = startX - endX
        const threshold = 100 // Minimum drag distance to trigger slide change

        if (Math.abs(dragDistance) > threshold) {
          const maxIndex = Math.max(0, totalCards - cardsPerView)
          if (dragDistance > 0 && currentSlideIndex < maxIndex) {
            // Dragged left - go to next slide
            currentSlideIndex++
          } else if (dragDistance < 0 && currentSlideIndex > 0) {
            // Dragged right - go to previous slide
            currentSlideIndex--
          }
          rafThrottledUpdateSliderPosition()
          rafThrottledUpdateArrowStates()
        }
      })

      container.addEventListener('mousemove', e => {
        if (!isDown) return
        e.preventDefault()
      })
    }

    // Initialize drag functionality after a short delay
    setTimeout(initVipDrag, 100)

    // Trial bonus function
    const claimTrialBonus = buttonElement => {
      console.log('Claiming trial bonus...')

      // Find the button element if not passed directly
      if (!buttonElement) {
        buttonElement = document.querySelector('.welcome-bonus-btn')
      }

      // Prevent multiple claims
      if (buttonElement.disabled || buttonElement.classList.contains('claimed')) return

      // Simple button state change only
      buttonElement.textContent = 'Claimed'
      buttonElement.disabled = true
      buttonElement.classList.add('claimed')

      // Apply persistent claimed state to banner without animations
      const banner = buttonElement.closest('.welcome-bonus-banner')
      if (banner) {
        banner.classList.add('bonus-claimed-state')
      }

      console.log('Welcome bonus claimed successfully!')
    }

    const claimReward = (type, buttonElement) => {
      console.log(`Claiming ${type} reward...`)

      // Find the button element if not passed directly
      if (!buttonElement) {
        buttonElement = document.querySelector(`[onclick*="${type}"]`)
      }

      // Prevent multiple claims
      if (buttonElement.disabled) return

      // Simple button state change only
      buttonElement.textContent = 'Claimed'
      buttonElement.disabled = true
      buttonElement.classList.add('claimed')

      console.log(`${type.charAt(0).toUpperCase() + type.slice(1)} reward claimed successfully!`)
    }

    document.querySelector('.apply-btn').onclick = async () => {
      const promoInput = document.getElementById('promoCode')
      const messageDiv = document.getElementById('promoMessage')

      if (!promoInput || !messageDiv) return

      const code = promoInput.value.trim().toUpperCase()

      if (!code) {
        messageDiv.textContent = 'Please enter a promo code'
        messageDiv.className = 'promo-message error'
        return
      }

      // Show loading state
      messageDiv.textContent = 'Applying promo code...'
      messageDiv.className = 'promo-message'
      promoInput.disabled = true

      try {
        // Get auth token from localStorage
        const authToken = localStorage.getItem('s7oryO9STV')

        if (!authToken) {
          messageDiv.textContent = 'Please log in to apply promo codes'
          messageDiv.className = 'promo-message error'
          promoInput.disabled = false
          return
        }

        // Make API request to activate promo code
        const response = await fetch(
          `${API_URL}/api/pronet/v1/bonus-promocodes/${encodeURIComponent(code)}/activations`,
          {
            method: 'POST',
            headers: {
              Authorization: `Bearer ${JSON.parse(authToken)}`,
              'Content-Type': 'application/json',
              Referer: window.location.href
            }
          }
        )

        const data = await response.json()

        if (data.success) {
          messageDiv.textContent = data.message || `Promo code "${code}" applied successfully!`
          messageDiv.className = 'promo-message success'
          promoInput.value = ''
        } else {
          messageDiv.textContent = data.error || 'Invalid promo code. Please try again.'
          messageDiv.className = 'promo-message error'
        }
      } catch (error) {
        console.error('Error applying promo code:', error)
        messageDiv.textContent = 'Network error. Please try again later.'
        messageDiv.className = 'promo-message error'
      } finally {
        promoInput.disabled = false
      }
    }

    // VIP Slider functionality
    let currentSlideIndex = 0
    let cardsPerView = 3
    let totalCards = 0

    // RAF throttle function for performance optimization
    function rafThrottle(fn) {
      let locked = false
      return (...args) => {
        if (locked) return
        locked = true
        requestAnimationFrame(() => {
          fn(...args)
          locked = false
        })
      }
    }

    const initializeVipSlider = () => {
      const container = document.getElementById('vipContainer')
      const grid = container?.querySelector('.vip-ranks-grid')
      const leftArrow = document.getElementById('vipNavLeft')
      const rightArrow = document.getElementById('vipNavRight')

      if (!grid || !leftArrow || !rightArrow) return

      totalCards = grid.children.length
      updateCardsPerView()
      rafThrottledUpdateSliderPosition()
      rafThrottledUpdateArrowStates()

      // Arrow click handlers
      leftArrow.addEventListener('click', () => {
        if (currentSlideIndex > 0) {
          currentSlideIndex--
          rafThrottledUpdateSliderPosition()
          rafThrottledUpdateArrowStates()
        }
      })

      rightArrow.addEventListener('click', () => {
        const maxIndex = Math.max(0, totalCards - cardsPerView)
        if (currentSlideIndex < maxIndex) {
          currentSlideIndex++
          rafThrottledUpdateSliderPosition()
          rafThrottledUpdateArrowStates()
        }
      })

      // Touch/swipe support
      let startX = 0
      let isDragging = false

      container.addEventListener('touchstart', e => {
        startX = e.touches[0].clientX
        isDragging = true
      })

      container.addEventListener('touchmove', e => {
        if (!isDragging) return
        e.preventDefault()
      })

      container.addEventListener('touchend', e => {
        if (!isDragging) return
        isDragging = false

        const endX = e.changedTouches[0].clientX
        const diff = startX - endX

        if (Math.abs(diff) > 50) {
          // Minimum swipe distance
          if (diff > 0) {
            // Swipe left - next
            rightArrow.click()
          } else {
            // Swipe right - previous
            leftArrow.click()
          }
        }
      })

      // Resize handler
      window.addEventListener('resize', () => {
        updateCardsPerView()
        currentSlideIndex = Math.min(currentSlideIndex, Math.max(0, totalCards - cardsPerView))
        rafThrottledUpdateSliderPosition()
        rafThrottledUpdateArrowStates()
      })

      // Add scroll event listener to synchronize arrow states with manual scrolling
      container.addEventListener('scroll', () => {
        // Calculate current slide index based on scroll position
        const cardWidth = 180 + 15 // Fixed card width + gap
        const scrollPosition = Math.abs(container.scrollLeft)
        const calculatedIndex = Math.round(scrollPosition / cardWidth)

        // Update current slide index if it changed
        if (calculatedIndex !== currentSlideIndex) {
          currentSlideIndex = Math.min(calculatedIndex, Math.max(0, totalCards - cardsPerView))
          rafThrottledUpdateArrowStates()
        }
      })
    }

    const updateCardsPerView = () => {
      const width = window.innerWidth
      const container = document.querySelector('.vip-ranks-container')

      if (width < 768) {
        cardsPerView = 3
        if (container) container.style.width = '570px' // Mobile: (180 × 3) + (15 × 2) = 570px
      } else if (width < 1200) {
        cardsPerView = 5
        if (container) container.style.width = '960px' // Tablet: (180 × 5) + (15 × 4) = 960px
      } else {
        cardsPerView = 7
        if (container) container.style.width = '1350px' // Desktop: (180 × 7) + (15 × 6) = 1350px
      }
    }

    const updateSliderPosition = () => {
      const grid = document.querySelector('.vip-ranks-grid')
      if (!grid) return

      // Fixed card width and gap for precise calculation
      const cardWidth = 180
      const gap = 15
      const cardWithGap = cardWidth + gap

      const translateX = -currentSlideIndex * cardWithGap
      grid.style.transform = `translateX(${translateX}px)`
    }

    const updateArrowStates = () => {
      const leftArrow = document.getElementById('vipNavLeft')
      const rightArrow = document.getElementById('vipNavRight')

      if (leftArrow) {
        leftArrow.disabled = currentSlideIndex === 0
      }

      if (rightArrow) {
        const maxIndex = Math.max(0, totalCards - cardsPerView)
        rightArrow.disabled = currentSlideIndex >= maxIndex
      }
    }

    // Create throttled versions of functions
    const rafThrottledUpdateSliderPosition = rafThrottle(updateSliderPosition)
    const rafThrottledUpdateArrowStates = rafThrottle(updateArrowStates)

    // VIP Perks data
    const vipPerksData = {
      BRONZE: {
        name: 'Bronze VIP',
        perks: ['Welcome bonus boost', 'Basic customer support', 'Monthly cashback', 'Birthday bonus']
      },
      SILVER: {
        name: 'Silver VIP',
        perks: [
          'Enhanced welcome bonus',
          'Priority customer support',
          'Weekly cashback',
          'Birthday bonus',
          'Exclusive tournaments'
        ]
      },
      GOLD: {
        name: 'Gold VIP',
        perks: [
          'Premium welcome bonus',
          'VIP customer support',
          'Daily cashback',
          'Birthday & anniversary bonus',
          'VIP tournaments',
          'Faster withdrawals'
        ]
      },
      PLATINUM: {
        name: 'Platinum VIP',
        perks: [
          'Elite welcome bonus',
          'Dedicated account manager',
          'Enhanced daily cashback',
          'Special occasion bonuses',
          'Exclusive VIP events',
          'Priority withdrawals',
          'Personal promotions'
        ]
      },
      DIAMOND: {
        name: 'Diamond VIP',
        perks: [
          'Ultimate welcome bonus',
          'Personal VIP manager',
          'Maximum cashback rates',
          'Luxury gifts & bonuses',
          'Private VIP events',
          'Instant withdrawals',
          'Custom promotions',
          'VIP travel packages'
        ]
      },
      ELITE: {
        name: 'Elite VIP',
        perks: [
          'Exclusive elite bonuses',
          'Elite concierge service',
          'Premium cashback rates',
          'Luxury lifestyle rewards',
          'Elite-only events',
          'No withdrawal limits',
          'Bespoke promotions',
          'Elite travel experiences',
          'Personal gaming advisor'
        ]
      },
      'MACRO BLACK': {
        name: 'Macro Black VIP',
        perks: [
          'Ultimate elite bonuses',
          'Black card concierge',
          'Unlimited cashback',
          'Exclusive luxury rewards',
          'Private gaming events',
          'Unlimited withdrawals',
          'Personalized everything',
          'World-class experiences',
          'Dedicated gaming team',
          'Invitation-only privileges'
        ]
      }
    }

    const viewPerks = tier => {
      console.log(`Viewing perks for ${tier}...`)
      showVipModal(tier)
    }

    // Promo code function
    const applyPromoCode = async () => {
      const promoInput = document.getElementById('promoCode')
      const messageDiv = document.getElementById('promoMessage')

      if (!promoInput || !messageDiv) return

      const code = promoInput.value.trim().toUpperCase()

      if (!code) {
        messageDiv.textContent = 'Please enter a promo code'
        messageDiv.className = 'promo-message error'
        return
      }

      // Show loading state
      messageDiv.textContent = 'Applying promo code...'
      messageDiv.className = 'promo-message'
      promoInput.disabled = true

      try {
        // Get auth token from localStorage
        const authToken = localStorage.getItem('s7oryo9stv')

        if (!authToken) {
          messageDiv.textContent = 'Please log in to apply promo codes'
          messageDiv.className = 'promo-message error'
          promoInput.disabled = false
          return
        }

        // Make API request to activate promo code
        const response = await fetch(
          `https://gateway.makroz.org/api/pronet/v1/bonus-promocodes/${encodeURIComponent(code)}/activations`,
          {
            method: 'POST',
            headers: {
              Authorization: `Bearer ${authToken}`,
              'Content-Type': 'application/json'
            }
          }
        )

        const data = await response.json()

        if (data.success) {
          messageDiv.textContent = data.message || `Promo code "${code}" applied successfully!`
          messageDiv.className = 'promo-message success'
          promoInput.value = ''
        } else {
          messageDiv.textContent = data.error || 'Invalid promo code. Please try again.'
          messageDiv.className = 'promo-message error'
        }
      } catch (error) {
        console.error('Error applying promo code:', error)
        messageDiv.textContent = 'Network error. Please try again later.'
        messageDiv.className = 'promo-message error'
      } finally {
        promoInput.disabled = false
      }
    }

    // API-based bonus claiming function
    const claimBonusFromAPI = async (bonusId, buttonElement) => {
      console.log(`Claiming bonus ${bonusId}...`)

      // Prevent multiple claims
      if (buttonElement.disabled || buttonElement.classList.contains('claimed')) return

      try {
        // Get auth token from localStorage
        const authToken = localStorage.getItem('s7oryO9STV')

        if (!authToken) {
          console.error('No auth token found')
          return
        }

        // Show loading state
        const originalText = buttonElement.textContent
        buttonElement.textContent = 'Claiming...'
        buttonElement.disabled = true

        const response = await fetch(`${API_URL}/api/pronet/v1/bonuses/${bonusId}/claims`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${JSON.parse(authToken)}`,
            'Content-Type': 'application/json',
            'Referer': window.location.href
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const result = await response.json()
        console.log('Claim bonus API response:', result)

        if (result.success) {
          // Success - update button state
          buttonElement.textContent = 'Claimed'
          buttonElement.classList.add('claimed')

          // Apply claimed state to parent container if it's a welcome bonus
          const banner = buttonElement.closest('.welcome-bonus-banner')
          if (banner) {
            banner.classList.add('bonus-claimed-state')
          }

          console.log('Bonus claimed successfully:', result)
        } else {
          // API returned error - restore button
          buttonElement.textContent = originalText
          buttonElement.disabled = false
          throw new Error(result.error || result.message || 'Failed to claim bonus')
        }
      } catch (error) {
        // Network error - restore button
        buttonElement.textContent = originalText
        buttonElement.disabled = false
        console.error('Error claiming bonus:', error)
      }
    }

    // Initialize event listeners
    const initializeEventListeners = () => {
      // Use event delegation for dynamically created content
      document.addEventListener('click', (e) => {
        // Handle claim buttons with bonus IDs (API-based)
        if (e.target.matches('.claim-btn[data-bonus-id]')) {
          const bonusId = e.target.getAttribute('data-bonus-id')
          if (bonusId) {
            claimBonusFromAPI(bonusId, e.target)
          }
          return
        }

        // Handle legacy claim buttons (static content fallback)
        if (e.target.matches('.claim-btn[data-reward-type]')) {
          const rewardType = e.target.getAttribute('data-reward-type')
          claimReward(rewardType, e.target)
          return
        }

        // Handle trial bonus button
        if (e.target.matches('#claim-trial-bonus')) {
          const bonusId = e.target.getAttribute('data-bonus-id')
          if (bonusId) {
            claimBonusFromAPI(bonusId, e.target)
          } else {
            claimTrialBonus(e.target)
          }
          return
        }

        // Handle VIP tier buttons
        if (e.target.matches('.view-perks-btn[data-vip-tier]')) {
          const tier = e.target.getAttribute('data-vip-tier')
          viewPerks(tier)
          return
        }

        // Handle promo code button
        if (e.target.matches('#apply-promo-btn')) {
          applyPromoCode()
          return
        }
      })

      // Promo code input (Enter key)
      const promoInput = document.getElementById('promoCode')
      if (promoInput) {
        promoInput.addEventListener('keypress', e => {
          if (e.key === 'Enter') {
            applyPromoCode()
          }
        })
      }
    }

    // Utility function to escape HTML
    const escapeHtml = (text) => {
      const div = document.createElement('div')
      div.textContent = text
      return div.innerHTML
    }

    // Fetch and render bonuses from API
    const fetchAndRenderBonuses = async () => {
      try {
        // Get auth token from localStorage
        const authToken = localStorage.getItem('s7oryO9STV')

        // if (!authToken) {
        //   console.log('No auth token found, using static content')
        //   return
        // }

        console.log('Fetching bonuses from API...')
        const response = await fetch(`${API_URL}/api/pronet/v1/site-claimable-bonuses`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Referer': window.location.href
          }
        })

        if (!response.ok) {
          if (response.status === 404) {
            console.log('Bonuses API endpoint not found, using static content')
            return
          }
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const result = await response.json()
        console.log('Bonuses API response:', result)

        if (result.success && result.data && Array.isArray(result.data)) {
          // Filter active bonuses
          const activeBonuses = result.data.filter(bonus => bonus.isActive === true)
          console.log('Active bonuses:', activeBonuses)

          // Group bonuses by type
          const timeBasedBonuses = activeBonuses.filter(bonus =>
            bonus.slotName && bonus.slotName.startsWith('time_')
          )
          const happyBonuses = activeBonuses.filter(bonus =>
            bonus.slotName && bonus.slotName.startsWith('happy_')
          )
          const welcomeBonuses = activeBonuses.filter(bonus =>
            bonus.slotName && bonus.slotName === 'welcome'
          )

          // Render bonuses
          renderTimeBasedBonuses(timeBasedBonuses)
          renderHappyBonuses(happyBonuses)
          renderWelcomeBonuses(welcomeBonuses)
        } else {
          console.log('No bonus data found, keeping static content')
        }
      } catch (error) {
        console.error('Error fetching bonuses:', error)
        console.log('Keeping static content due to API error')
      }
    }

    // Render time-based bonuses (2-card layout)
    const renderTimeBasedBonuses = (bonuses) => {
      const rewardsGrid = document.querySelector('.rewards-grid')
      if (!rewardsGrid) return

      if (bonuses.length === 0) {
        console.log('No time-based bonuses found, keeping static content')
        return
      }

      rewardsGrid.innerHTML = bonuses.map(bonus => `
        <div class="reward-card ${bonus.type || 'instant'}">
          <div class="sweep-light"></div>
          <div class="reward-content">
            <div class="reward-header">
              <h3>${escapeHtml(bonus.name || 'Bonus Reward')}</h3>
              <p class="reward-description">${escapeHtml(bonus.description || 'Claim your bonus now!')}</p>
            </div>
            <div class="reward-icon-wrapper">
              <div class="reward-icon">
                <div class="coin-stack"></div>
              </div>
            </div>
            <div class="reward-amount">
              <span class="currency-symbol">₺</span>${escapeHtml(bonus.amount || '0.00')}
            </div>
            <button class="claim-btn" data-reward-type="${bonus.type || 'instant'}" data-bonus-id="${bonus.id}">
              Claim Reward
            </button>
          </div>
        </div>
      `).join('')

      // Re-initialize coin icons for new content
      setTimeout(initializeRewardIcons, 100)
    }

    // Render happy bonuses (3-card layout)
    const renderHappyBonuses = (bonuses) => {
      const happyHoursGrid = document.querySelector('.happy-hours-grid')
      if (!happyHoursGrid) return

      if (bonuses.length === 0) {
        console.log('No happy bonuses found, keeping static content')
        return
      }

      happyHoursGrid.innerHTML = bonuses.map(bonus => `
        <div class="happy-hour-card">
          <div class="happy-hour-time">
            <span class="time-label">${escapeHtml(bonus.name || 'Happy Hour')}</span>
            <span class="time-range">${escapeHtml(bonus.timeRange || 'All Day')}</span>
          </div>
          <div class="happy-hour-bonus">
            <div class="bonus-icon">${getHappyBonusIcon(bonus.type)}</div>
            <div class="bonus-details">
              <div class="bonus-title">${escapeHtml(bonus.name || 'Happy Bonus')}</div>
              <div class="bonus-amount">
                <span class="currency-symbol">₺</span>${escapeHtml(bonus.amount || '0.00')}
              </div>
            </div>
          </div>
          <button class="claim-btn happy-hour-btn" data-bonus-id="${bonus.id}" data-happy-hour="${bonus.slotName}">
            Claim Bonus
          </button>
        </div>
      `).join('')
    }

    // Render welcome bonuses (full-width)
    const renderWelcomeBonuses = (bonuses) => {
      const welcomeBanner = document.querySelector('.welcome-bonus-banner')
      if (!welcomeBanner) return

      if (bonuses.length === 0) {
        console.log('No welcome bonuses found, keeping static content')
        return
      }

      const welcomeBonus = bonuses[0] // Take the first welcome bonus

      welcomeBanner.innerHTML = `
        <h2>Welcome Bonus</h2>
        <div class="amount">${escapeHtml(welcomeBonus.amount || '250')} TL</div>
        <p style="font-size: 16px; color: rgba(255, 255, 255, 0.8); margin-bottom: 24px; position: relative; z-index: 1;">
          ${escapeHtml(welcomeBonus.description || 'New players get an instant bonus! Start your winning journey today.')}
        </p>
        <button class="welcome-bonus-btn" id="claim-trial-bonus" data-bonus-id="${welcomeBonus.id}">
          Claim Your Bonus
        </button>
      `
    }

    // Get appropriate icon for happy bonus type
    const getHappyBonusIcon = (type) => {
      const iconMap = {
        'freespin': '🎰',
        'cashback': '💰',
        'deposit': '💳',
        'lossback': '🔄',
        'reload': '⚡',
        'weekend': '🎉',
        'daily': '☀️',
        'weekly': '📅',
        'monthly': '🗓️'
      }
      return iconMap[type] || '🎁'
    }

    // Initialize event listeners after a short delay to ensure DOM is ready
    setTimeout(initializeEventListeners, 100)

    // Fetch and render bonuses after DOM is ready
    setTimeout(fetchAndRenderBonuses, 200)

    const showVipModal = tier => {
      const modal = document.getElementById('vipModal')
      const title = document.getElementById('vipModalTitle')
      const body = document.getElementById('vipModalBody')

      if (!modal || !title || !body) return

      // Handle MakroBlack naming convention
      let tierKey = tier.toUpperCase()
      if (tierKey === 'MAKROBLACK') {
        tierKey = 'MACRO BLACK'
      }

      const tierData = vipPerksData[tierKey]
      if (!tierData) return

      // Set modal content
      title.textContent = `${tierData.name} Benefits`

      body.innerHTML = `
        <div class="vip-modal-tier-name">${tierData.name}</div>
        <ul class="vip-modal-perks-list">
          ${tierData.perks
            .map(
              perk => `
            <li class="vip-modal-perk">
              <svg class="vip-modal-perk-icon" viewBox="0 0 24 24" fill="none">
                <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <span>${perk}</span>
            </li>
          `
            )
            .join('')}
        </ul>
      `

      // Show modal
      modal.classList.add('active')
      document.body.style.overflow = 'hidden'

      // Focus management for accessibility
      const closeButton = document.getElementById('vipModalClose')
      if (closeButton) {
        closeButton.focus()
      }
    }

    const hideVipModal = () => {
      const modal = document.getElementById('vipModal')
      if (modal) {
        modal.classList.remove('active')
        document.body.style.overflow = ''
      }
    }

    // Modal event listeners
    const initializeVipModal = () => {
      const modal = document.getElementById('vipModal')
      const backdrop = document.getElementById('vipModalBackdrop')
      const closeButton = document.getElementById('vipModalClose')

      if (closeButton) {
        closeButton.addEventListener('click', hideVipModal)
      }

      if (backdrop) {
        backdrop.addEventListener('click', hideVipModal)
      }

      // Escape key to close
      document.addEventListener('keydown', e => {
        if (e.key === 'Escape' && modal?.classList.contains('active')) {
          hideVipModal()
        }
      })
    }

    // Initialize everything after DOM is ready
    setTimeout(() => {
      initializeVipSlider()
      initializeVipModal()
    }, 500)

    // Cleanup function
    kill(() => {
      // Remove CSS link
      const cssLink = document.querySelector('link[href="/rewards-page/1_content.css"]')
      if (cssLink) {
        cssLink.remove()
      }
    })
  }
})
