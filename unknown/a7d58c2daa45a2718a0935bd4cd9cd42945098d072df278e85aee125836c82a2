function initializePage(selector, filename) {
  let container = null
  let unload = null
  let timeout = null
  
  const inject = async () => {
    unload?.()
    window.dispatchEvent(new Event(`@makrobet/unload/${filename}`))

    console.log('@core Injecting...')
    let data = await fetch(`http://127.0.0.1:6969/${filename}.html`).then(r => r.text())
    container.innerHTML = data

    const script = document.createElement('script')
    script.src = `http://127.0.0.1:6969/${filename}.js`
    script.defer = true
    document.head.append(script)

    const style = document.createElement('link')
    style.rel = 'stylesheet'
    style.href = `http://127.0.0.1:6969/${filename}.css`
    document.head.append(style)

    console.log(`@core Injected ${selector}!`)

    return () => {
      console.log('@core Unloading...')

      container.innerHTML = ''
      script.remove()
      style.remove()
    }
  }

  setTimeout(async () => {
    const ws = new WebSocket('http://127.0.0.1:6969/ws')

    ws.onopen = () => {
      console.log('@core Connected to WebSocket')
    }

    ws.onmessage = (event) => {
      if (timeout) {
        clearTimeout(timeout)
      }

      timeout = setTimeout(async () => {
        unload = await inject()
      }, 300)
    }

    ws.onerror = (err) => {
      console.error('@core Error connecting to WebSocket', err)
    }

    container = document.querySelector(selector)

    if (container) {
      unload = await inject(container)
    } else {
      const observer = new MutationObserver(async (mutationList) => {
        for (const mutation of mutationList) {
          if (mutation.type !== 'childList') {
            continue
          }

          container = document.querySelector(selector)
          if (container) {
            observer.disconnect()
            unload = await inject()
            break
          }
        }
      })
      observer.observe(document.body, { childList: true, subtree: true })
    }
  }, 1000)
}

