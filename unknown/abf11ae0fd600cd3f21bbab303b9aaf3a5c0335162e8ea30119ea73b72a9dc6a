const express = require('express');
const { WebSocketServer } = require('ws');
const chokidar = require('chokidar');
const fs = require('fs');
const path = require('path');
const http = require('http');

const app = express();
const server = http.createServer(app);
const PORT = 6969;

// CORS middleware to allow any requests, headers, and methods
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', '*');
  res.header('Access-Control-Allow-Headers', '*');
  res.header('Access-Control-Allow-Credentials', 'true');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

// Create WebSocket server
const wss = new WebSocketServer({
  server,
  path: '/ws'
});

// Store connected clients
const clients = new Set();

// WebSocket connection handling
wss.on('connection', (ws, req) => {
  console.log('Client connected to WebSocket');
  clients.add(ws);

  ws.on('close', () => {
    console.log('Client disconnected from WebSocket');
    clients.delete(ws);
  });

  ws.on('error', (error) => {
    console.error('WebSocket error:', error);
    clients.delete(ws);
  });
});

// Function to broadcast message to all connected clients
function broadcast(message) {
  const messageStr = typeof message === 'string' ? message : JSON.stringify(message);
  console.log(`Broadcasting to ${clients.size} clients:`, messageStr);

  clients.forEach(client => {
    if (client.readyState === client.OPEN) {
      client.send(messageStr);
    }
  });
}

// Watch the _dist folder for changes
const distPath = path.join(__dirname, 'dist');
console.log('Watching directory:', distPath);

const watcher = chokidar.watch(distPath, {
  ignored: /[\/\\]\./,
  persistent: true,
  ignoreInitial: true
});

watcher
  .on('add', filePath => {
    console.log(`File added: ${filePath}`);
    broadcast({
      type: 'file_added',
      file: path.basename(filePath),
      path: filePath,
      timestamp: Date.now()
    });
  })
  .on('change', filePath => {
    console.log(`File changed: ${filePath}`);
    broadcast({
      type: 'file_changed',
      file: path.basename(filePath),
      path: filePath,
      timestamp: Date.now()
    });
  })
  .on('unlink', filePath => {
    console.log(`File removed: ${filePath}`);
    broadcast({
      type: 'file_removed',
      file: path.basename(filePath),
      path: filePath,
      timestamp: Date.now()
    });
  })
  .on('error', error => {
    console.error('Watcher error:', error);
  });

// API route to get list of JS scripts in _dist folder
app.get('/{*splat}.html', (req, res) => {
  try {
    let html = fs.readFileSync(path.join(distPath, req.path), 'utf8');

    let index = html.indexOf('<script')
    while (index !== -1) {
      const end = html.indexOf('</script>', index)
      html = html.slice(0, index) + html.slice(end + 9)
      index = html.indexOf('<script')
    }

    index = html.indexOf('<style>')
    while (index !== -1) {
      const end = html.indexOf('</style>', index)
      html = html.slice(0, index) + html.slice(end + 8)
      index = html.indexOf('<style>')
    }

    res.send(html.trim());
  } catch (error) {
    console.error('Error reading _dist directory:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to read scripts directory',
      message: error.message
    });
  }
});

app.get('/{*splat}.js', (req, res) => {
  try {
    let html = fs.readFileSync(path.join(distPath, req.path.replace('.js', '.html')), 'utf8');

    const scripts = []

    let index = html.indexOf('<script')
    while (index !== -1) {
      const end = html.indexOf('</script>', index)
      scripts.push(html.slice(html.indexOf('>', index) + 1, end))
      html = html.slice(0, index) + html.slice(end + 9)
      index = html.indexOf('<script')
    }

    res.setHeader('Content-Type', 'text/javascript');
    res.send(scripts.join('\n\n').trim());
  } catch (error) {
    console.error('Error reading _dist directory:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to read scripts directory',
      message: error.message
    });
  }
});

app.get('/{*splat}.css', (req, res) => {
  try {
    let html = fs.readFileSync(path.join(distPath, req.path.replace('.css', '.html')), 'utf8');

    const styles = []

    let index = html.indexOf('<style>')
    while (index !== -1) {
      const end = html.indexOf('</style>', index)
      styles.push(html.slice(index + 7, end))
      html = html.slice(0, index) + html.slice(end + 8)
      index = html.indexOf('<style>')
    }

    res.setHeader('Content-Type', 'text/css');
    res.send(styles.join('\n\n').trim());
  } catch (error) {
    console.error('Error reading _dist directory:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to read scripts directory',
      message: error.message
    });
  }
});

// Start the server
server.listen(PORT, '127.0.0.1', () => {
  console.log(`Server running on http://127.0.0.1:${PORT}`);
  console.log(`WebSocket server available at ws://127.0.0.1:${PORT}/ws`);
  console.log(`API endpoints:`);
  console.log(`  GET /scripts - List JS files in _dist folder`);
  console.log(`  GET /dist/<filename> - Serve static files from _dist`);
  console.log(`  GET /health - Server health check`);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\nShutting down server...');
  watcher.close();
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});
