// Providers Marquee Section JavaScript
window.$LANDING.onLifecycle({ selector: `.makrobet-landing`, page: /^\/\w+$/, onMount: (_, __, kill) => {
  const CACHE_KEY = `$landing_providers`;
  const CACHE_TIMESTAMP_KEY = `$landing_providers_timestamp`;
  const CACHE_DURATION = 3 * 60 * 60 * 1000; // 3 hours in milliseconds
  const API_URL = `${window.origin}/odin/api/user/casinoapi/getReservedVendors/ordered`;

  const providersRow1 = document.getElementById(`providers-row-1`);
  const providersRow2 = document.getElementById(`providers-row-2`);
  const providersRow3 = document.getElementById(`providers-row-3`);
  const providersSection = document.querySelector('.providers-marquee');

  // Check if elements exist
  if (!providersRow1 || !providersRow2 || !providersRow3 || !providersSection) {
    console.error(`Providers: Required DOM elements not found`);
    return;
  }

  // Custom provider card creation function
  function createProviderCard(provider) {
    const imageUrl = `//v3.pro1staticserv.com/common/assets/images/casino/22x22/${provider.code}.png`;
    const fallbackImageUrl = `data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDQiIGhlaWdodD0iNDQiIHZpZXdCb3g9IjAgMCA0NCA0NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQ0IiBoZWlnaHQ9IjQ0IiBmaWxsPSIjMmIyYjRmIi8+Cjx0ZXh0IHg9IjIyIiB5PSIyMiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZmlsbD0iI2ZiZDEyZCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjEwIj5Mb2dvPC90ZXh0Pgo8L3N2Zz4=`;

    // Create main card container
    const providerCard = document.createElement(`div`);
    providerCard.className = `provider-card`;
    providerCard.setAttribute(`data-provider-id`, provider.id);
    providerCard.setAttribute(`data-provider-code`, provider.code);
    providerCard.setAttribute(`tabindex`, `0`);
    providerCard.setAttribute(`role`, `button`);

    // Create image element
    const providerImage = document.createElement(`img`);
    providerImage.src = imageUrl;
    providerImage.alt = provider.name;
    providerImage.className = `provider-image`;
    providerImage.addEventListener(`error`, function() {
      this.src = fallbackImageUrl;
    });

    // Create name element
    const providerName = document.createElement(`div`);
    providerName.className = `provider-name`;
    providerName.textContent = provider.name;

    // Assemble the card
    providerCard.appendChild(providerImage);
    providerCard.appendChild(providerName);

    return providerCard;
  }

  // Function to distribute providers across 3 rows
  function distributeProviders(providers) {
    const row1 = [];
    const row2 = [];
    const row3 = [];

    providers.forEach((provider, index) => {
      if (index % 3 === 0) {
        row1.push(provider);
      } else if (index % 3 === 1) {
        row2.push(provider);
      } else {
        row3.push(provider);
      }
    });

    return { row1, row2, row3 };
  }

  // Function to populate a row with providers (duplicate for seamless loop)
  function populateRow(rowElement, providers) {
    rowElement.innerHTML = ``;

    // Create the main set of cards
    const mainSet = document.createElement(`div`);
    mainSet.style.display = `flex`;
    mainSet.style.gap = `20px`;

    providers.forEach(provider => {
      const card = createProviderCard(provider);
      mainSet.appendChild(card);
    });

    // Create duplicate set for seamless loop
    const duplicateSet = mainSet.cloneNode(true);

    // Append both sets to the row
    rowElement.appendChild(mainSet);
    rowElement.appendChild(duplicateSet);
  }

  // Function to load providers data
  async function loadProviders() {
    try {
      // Check cache first
      const cachedData = localStorage.getItem(CACHE_KEY);
      const cachedTimestamp = localStorage.getItem(CACHE_TIMESTAMP_KEY);
      const now = Date.now();

      if (cachedData && cachedTimestamp && (now - parseInt(cachedTimestamp)) < CACHE_DURATION) {
        const providers = JSON.parse(cachedData);
        displayProviders(providers);
        return;
      }

      // Fetch from API
      const response = await fetch(API_URL, {
        method: `POST`,
        headers: {
          [`Content-Type`]: `application/json`,
        },
        body: JSON.stringify({
          requestBody: {
            currencyId: 1
          },
          languageId: 1,
          device: `d`
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.success && data.data && data.data.vendors) {
        const providers = data.data.vendors;

        // Cache the data
        localStorage.setItem(CACHE_KEY, JSON.stringify(providers));
        localStorage.setItem(CACHE_TIMESTAMP_KEY, now.toString());

        displayProviders(providers);
      } else {
        throw new Error(`Invalid response format`);
      }
    } catch (error) {
      console.error(`Error loading providers:`, error);
      // Show error state or fallback
    }
  }

  // Function to display providers in 3 rows
  function displayProviders(providers) {
    const { row1, row2, row3 } = distributeProviders(providers);

    populateRow(providersRow1, row1);
    populateRow(providersRow2, row2);
    populateRow(providersRow3, row3);

    // Set up scroll-based visibility for mobile
    setupScrollVisibility();
  }

  // Function to check if device is mobile
  function isMobileDevice() {
    return window.innerWidth <= 768;
  }

  // Function to setup scroll-based visibility
  function setupScrollVisibility() {
    if (!isMobileDevice()) {
      // On desktop, always show the section
      providersSection.classList.add('visible');
      return;
    }

    // Check if Intersection Observer is supported
    if ('IntersectionObserver' in window) {
      // Use Intersection Observer for better performance
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            // Section is in viewport, make it visible
            providersSection.classList.add('visible');
            // Once visible, we can stop observing
            observer.unobserve(entry.target);
          }
        });
      }, {
        // Trigger when 20% of the section is visible
        threshold: 0.2,
        // Start observing 100px before the section comes into view
        rootMargin: '100px 0px'
      });

      observer.observe(providersSection);

      // Handle window resize to re-evaluate mobile state
      const handleResize = () => {
        if (!isMobileDevice()) {
          // Switched to desktop, always show
          providersSection.classList.add('visible');
          observer.unobserve(providersSection);
          window.removeEventListener('resize', handleResize);
        }
      };

      window.addEventListener('resize', handleResize);
    } else {
      // Fallback for browsers without Intersection Observer
      const checkScroll = () => {
        const sectionTop = providersSection.offsetTop;
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const windowHeight = window.innerHeight;

        // Show section when it's 100px away from entering viewport
        if (scrollTop + windowHeight >= sectionTop - 100) {
          providersSection.classList.add('visible');
          window.removeEventListener('scroll', checkScroll);
        }
      };

      window.addEventListener('scroll', checkScroll);
      // Check immediately in case section is already in view
      checkScroll();

      // Handle window resize
      const handleResize = () => {
        if (!isMobileDevice()) {
          providersSection.classList.add('visible');
          window.removeEventListener('scroll', checkScroll);
          window.removeEventListener('resize', handleResize);
        }
      };

      window.addEventListener('resize', handleResize);
    }
  }

  // Initialize
  // Set initial state for mobile devices
  if (isMobileDevice()) {
    providersSection.classList.remove('visible');
  } else {
    providersSection.classList.add('visible');
  }

  loadProviders();

  window.addEventListener('@makrobet/unload/landing', () => {
    kill()
  }, { once: true })
}});
