/* Epic Dark Makrobet Landing Background with Advanced Visual Effects */
.makrobet-landing {
  background:
    /* Primary ambient orbs */
    radial-gradient(ellipse 600px 400px at 15% 25%, rgba(var(--primary-color), 0.12) 0%, transparent 50%),
    radial-gradient(ellipse 500px 600px at 85% 75%, rgba(var(--secondary-color), 0.15) 0%, transparent 50%),
    radial-gradient(ellipse 400px 300px at 70% 20%, rgba(var(--primary-color), 0.08) 0%, transparent 60%),
    radial-gradient(ellipse 350px 450px at 20% 80%, rgba(var(--secondary-color), 0.1) 0%, transparent 55%),
    radial-gradient(ellipse 300px 200px at 50% 10%, rgba(var(--primary-color), 0.06) 0%, transparent 65%),
    radial-gradient(ellipse 250px 350px at 90% 50%, rgba(var(--secondary-color), 0.08) 0%, transparent 60%),
    /* Deep space gradient with more complexity */
    linear-gradient(135deg,
      #0a0a1a 0%,
      #1a1a2e 15%,
      #16213e 30%,
      #1e1e3f 45%,
      #2a2a4a 60%,
      #1e1e3f 75%,
      #16213e 85%,
      #0f0f23 100%),
    /* Base atmospheric layer */
    linear-gradient(45deg, #0a0a1a, #1a1a2e);
  position: relative;
  min-height: 100vh;
  overflow: hidden;
}

.makrobet-landing::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    /* Floating particle system */
    radial-gradient(circle 4px at 12% 20%, rgba(var(--primary-color), 0.8) 0%, transparent 50%),
    radial-gradient(circle 2px at 88% 25%, rgba(var(--secondary-color), 0.6) 0%, transparent 50%),
    radial-gradient(circle 3px at 25% 75%, rgba(var(--primary-color), 0.7) 0%, transparent 50%),
    radial-gradient(circle 1px at 75% 80%, rgba(var(--secondary-color), 0.9) 0%, transparent 50%),
    radial-gradient(circle 2px at 45% 15%, rgba(var(--primary-color), 0.5) 0%, transparent 50%),
    radial-gradient(circle 3px at 65% 60%, rgba(var(--secondary-color), 0.7) 0%, transparent 50%),
    radial-gradient(circle 1px at 35% 40%, rgba(var(--primary-color), 0.8) 0%, transparent 50%),
    radial-gradient(circle 2px at 55% 85%, rgba(var(--secondary-color), 0.6) 0%, transparent 50%),
    radial-gradient(circle 1px at 15% 55%, rgba(var(--primary-color), 0.9) 0%, transparent 50%),
    radial-gradient(circle 2px at 85% 45%, rgba(var(--secondary-color), 0.5) 0%, transparent 50%),
    /* Energy field waves */
    radial-gradient(ellipse 800px 400px at 30% 70%, rgba(var(--primary-color), 0.04) 0%, transparent 70%),
    radial-gradient(ellipse 600px 300px at 70% 30%, rgba(var(--secondary-color), 0.03) 0%, transparent 70%),
    /* Ambient energy streams */
    linear-gradient(45deg,
      transparent 0%,
      rgba(var(--primary-color), 0.02) 25%,
      transparent 50%,
      rgba(var(--secondary-color), 0.03) 75%,
      transparent 100%);
  background-size:
    200px 200px, 150px 150px, 250px 250px, 100px 100px, 180px 180px,
    220px 220px, 120px 120px, 160px 160px, 90px 90px, 140px 140px,
    1600px 800px, 1200px 600px, 100% 100%;
  pointer-events: none;
  animation: floatingParticles 25s ease-in-out infinite;
}

.makrobet-landing::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    /* Subtle grid pattern */
    repeating-linear-gradient(90deg,
      transparent 0px,
      rgba(var(--primary-color), 0.015) 1px,
      transparent 2px,
      transparent 120px),
    repeating-linear-gradient(0deg,
      transparent 0px,
      rgba(var(--secondary-color), 0.01) 1px,
      transparent 2px,
      transparent 150px),
    /* Diagonal energy lines */
    repeating-linear-gradient(45deg,
      transparent 0px,
      rgba(var(--primary-color), 0.008) 1px,
      transparent 3px,
      transparent 300px),
    repeating-linear-gradient(-45deg,
      transparent 0px,
      rgba(var(--secondary-color), 0.008) 1px,
      transparent 3px,
      transparent 350px),
    /* Atmospheric noise */
    radial-gradient(circle at 25% 25%, rgba(var(--primary-color), 0.02) 0%, transparent 30%),
    radial-gradient(circle at 75% 75%, rgba(var(--secondary-color), 0.015) 0%, transparent 30%);
  pointer-events: none;
  opacity: 0.4;
  animation: energyGrid 30s linear infinite;
}

@media screen and (max-width: 768px) {

  .makrobet-landing::before,
  .makrobet-landing::after {
    animation: none;
  }
}

/* Advanced Animation Keyframes */
@keyframes floatingParticles {

  0%,
  100% {
    transform: translateX(0) translateY(0);
    opacity: 1;
  }

  20% {
    transform: translateX(12px) translateY(-8px);
    opacity: 0.8;
  }

  40% {
    transform: translateX(-6px) translateY(15px);
    opacity: 0.9;
  }

  60% {
    transform: translateX(18px) translateY(-12px);
    opacity: 0.7;
  }

  80% {
    transform: translateX(-10px) translateY(6px);
    opacity: 0.85;
  }
}

@keyframes energyGrid {
  0% {
    transform: translateX(0) translateY(0);
    opacity: 0.4;
  }

  25% {
    transform: translateX(5px) translateY(-3px);
    opacity: 0.5;
  }

  50% {
    transform: translateX(-3px) translateY(7px);
    opacity: 0.3;
  }

  75% {
    transform: translateX(8px) translateY(-5px);
    opacity: 0.45;
  }

  100% {
    transform: translateX(0) translateY(0);
    opacity: 0.4;
  }
}
